using System;
using System.Collections.Generic;
using UnityEngine;

namespace SultansGameClone.Core
{
    /// <summary>
    /// 卡牌数据模型 - 苏丹的游戏核心卡牌系统
    /// 基于真实游戏数据结构设计
    /// </summary>
    [Serializable]
    public class Card
    {
        [Header("基础信息")]
        public int id;                      // 卡牌ID
        public string uid;                  // 唯一标识符
        public string name;                 // 卡牌名称
        public string title;                // 卡牌标题
        public string description;          // 卡牌描述
        public string flavorText;           // 风味文本
        
        [Header("卡牌类型")]
        public CardType cardType;           // 卡牌类型
        public CardRarity rarity;           // 稀有度
        public int cost;                    // 使用成本
        
        [Header("属性值")]
        public int physique;                // 体魄
        public int charm;                   // 魅力
        public int intelligence;            // 智慧
        public int fortune;                 // 财富
        public int spirit;                  // 精神
        
        [Header("视觉资源")]
        public string iconPath;             // 图标路径
        public string artworkPath;          // 卡牌插画路径
        public Sprite cardSprite;           // 卡牌精灵
        
        [Header("游戏状态")]
        public bool isEquipped;             // 是否装备
        public bool isLocked;               // 是否锁定
        public bool isUsed;                 // 是否已使用
        public bool isSelected;             // 是否选中
        public bool canBeUsed;              // 是否可使用
        
        [Header("标签系统")]
        public List<string> tags;           // 卡牌标签
        public List<CardEffect> effects;    // 卡牌效果
        
        [Header("升级系统")]
        public int level = 1;               // 卡牌等级
        public int experience = 0;          // 经验值
        public int maxLevel = 10;           // 最大等级
        
        [Header("仪式相关")]
        public List<int> requiredRites;     // 需要的仪式ID
        public List<int> unlockedRites;     // 解锁的仪式ID
        
        // 构造函数
        public Card()
        {
            uid = Guid.NewGuid().ToString();
            tags = new List<string>();
            effects = new List<CardEffect>();
            requiredRites = new List<int>();
            unlockedRites = new List<int>();
            canBeUsed = true;
        }
        
        /// <summary>
        /// 创建卡牌副本
        /// </summary>
        public Card Clone()
        {
            var clone = new Card
            {
                id = this.id,
                uid = Guid.NewGuid().ToString(), // 新的唯一ID
                name = this.name,
                title = this.title,
                description = this.description,
                flavorText = this.flavorText,
                cardType = this.cardType,
                rarity = this.rarity,
                cost = this.cost,
                physique = this.physique,
                charm = this.charm,
                intelligence = this.intelligence,
                fortune = this.fortune,
                spirit = this.spirit,
                iconPath = this.iconPath,
                artworkPath = this.artworkPath,
                cardSprite = this.cardSprite,
                level = this.level,
                experience = this.experience,
                maxLevel = this.maxLevel,
                tags = new List<string>(this.tags),
                effects = new List<CardEffect>(this.effects),
                requiredRites = new List<int>(this.requiredRites),
                unlockedRites = new List<int>(this.unlockedRites)
            };
            
            return clone;
        }
        
        /// <summary>
        /// 获取总属性值
        /// </summary>
        public int GetTotalAttributes()
        {
            return physique + charm + intelligence + fortune + spirit;
        }
        
        /// <summary>
        /// 获取指定属性值（包含等级加成）
        /// </summary>
        public int GetAttribute(AttributeType attributeType)
        {
            int baseValue = attributeType switch
            {
                AttributeType.Physique => physique,
                AttributeType.Charm => charm,
                AttributeType.Intelligence => intelligence,
                AttributeType.Fortune => fortune,
                AttributeType.Spirit => spirit,
                _ => 0
            };
            
            // 等级加成
            int levelBonus = (level - 1) * 2;
            return baseValue + levelBonus;
        }
        
        /// <summary>
        /// 检查是否有指定标签
        /// </summary>
        public bool HasTag(string tag)
        {
            return tags.Contains(tag);
        }
        
        /// <summary>
        /// 添加标签
        /// </summary>
        public void AddTag(string tag)
        {
            if (!tags.Contains(tag))
            {
                tags.Add(tag);
            }
        }
        
        /// <summary>
        /// 移除标签
        /// </summary>
        public void RemoveTag(string tag)
        {
            tags.Remove(tag);
        }
        
        /// <summary>
        /// 升级卡牌
        /// </summary>
        public bool LevelUp()
        {
            if (level < maxLevel)
            {
                level++;
                experience = 0;
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 添加经验值
        /// </summary>
        public bool AddExperience(int exp)
        {
            experience += exp;
            int requiredExp = level * 100; // 每级需要的经验值
            
            if (experience >= requiredExp && level < maxLevel)
            {
                return LevelUp();
            }
            return false;
        }
        
        /// <summary>
        /// 检查是否可以使用
        /// </summary>
        public bool CanUse()
        {
            return canBeUsed && !isLocked && !isUsed;
        }
        
        /// <summary>
        /// 应用卡牌效果
        /// </summary>
        public void ApplyEffects(Player target)
        {
            foreach (var effect in effects)
            {
                effect.Apply(target, this);
            }
        }
        
        /// <summary>
        /// 获取卡牌显示信息
        /// </summary>
        public string GetDisplayInfo()
        {
            return $"{name} (Lv.{level})\n" +
                   $"体魄:{GetAttribute(AttributeType.Physique)} " +
                   $"魅力:{GetAttribute(AttributeType.Charm)} " +
                   $"智慧:{GetAttribute(AttributeType.Intelligence)} " +
                   $"财富:{GetAttribute(AttributeType.Fortune)} " +
                   $"精神:{GetAttribute(AttributeType.Spirit)}\n" +
                   $"{description}";
        }
    }
    
    /// <summary>
    /// 卡牌类型枚举
    /// </summary>
    public enum CardType
    {
        Character,      // 角色卡
        Equipment,      // 装备卡
        Event,          // 事件卡
        Skill,          // 技能卡
        Ritual,         // 仪式卡
        Story           // 剧情卡
    }
    
    /// <summary>
    /// 卡牌稀有度
    /// </summary>
    public enum CardRarity
    {
        Common,         // 普通
        Uncommon,       // 不常见
        Rare,           // 稀有
        Epic,           // 史诗
        Legendary       // 传说
    }
    
    /// <summary>
    /// 属性类型
    /// </summary>
    public enum AttributeType
    {
        Physique,       // 体魄
        Charm,          // 魅力
        Intelligence,   // 智慧
        Fortune,        // 财富
        Spirit          // 精神
    }
    
    /// <summary>
    /// 卡牌效果
    /// </summary>
    [Serializable]
    public class CardEffect
    {
        public EffectType effectType;
        public AttributeType targetAttribute;
        public int value;
        public string description;
        
        public virtual void Apply(Player target, Card source)
        {
            // 基础效果应用逻辑
            switch (effectType)
            {
                case EffectType.AttributeBonus:
                    // 属性加成效果
                    break;
                case EffectType.Healing:
                    // 治疗效果
                    break;
                case EffectType.DrawCard:
                    // 抽卡效果
                    break;
            }
        }
    }
    
    /// <summary>
    /// 效果类型
    /// </summary>
    public enum EffectType
    {
        AttributeBonus,     // 属性加成
        Healing,            // 治疗
        DrawCard,           // 抽卡
        DiscardCard,        // 弃牌
        GainExperience,     // 获得经验
        UnlockRite          // 解锁仪式
    }
}
