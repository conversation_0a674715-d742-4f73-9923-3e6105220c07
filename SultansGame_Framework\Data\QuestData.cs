using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using SultansGameClone.Core;

namespace SultansGameClone.Systems
{
    /// <summary>
    /// 任务数据模型 - 定义任务的所有属性和行为
    /// </summary>
    [Serializable]
    [CreateAssetMenu(fileName = "New Quest", menuName = "Sultan's Game/Quest Data")]
    public class QuestData : ScriptableObject
    {
        [Header("基础信息")]
        public int id;                              // 任务ID
        public string questName;                    // 任务名称
        public string description;                  // 任务描述
        public string longDescription;              // 详细描述
        
        [Header("任务类型")]
        public QuestType questType;                 // 任务类型
        public QuestPriority priority;              // 优先级
        public bool isMainQuest = false;            // 是否主线任务
        public bool isRepeatable = false;           // 是否可重复
        
        [Header("任务目标")]
        public List<QuestObjective> objectives = new List<QuestObjective>(); // 任务目标
        
        [Header("条件设置")]
        public List<int> prerequisiteQuests = new List<int>();              // 前置任务
        public int minPlayerLevel = 1;                                      // 最低玩家等级
        public int maxPlayerLevel = 999;                                    // 最高玩家等级
        public List<int> requiredCards = new List<int>();                   // 需要的卡牌
        
        [Header("奖励设置")]
        public List<QuestReward> rewards = new List<QuestReward>();         // 奖励列表
        
        [Header("时间限制")]
        public bool hasTimeLimit = false;           // 是否有时间限制
        public int timeLimitHours = 24;             // 时间限制（小时）
        
        [Header("视觉资源")]
        public Sprite questIcon;                    // 任务图标
        public Sprite questImage;                   // 任务图片
        public Color questColor = Color.white;      // 主题颜色
        
        [Header("运行时状态")]
        [SerializeField] private bool _isActive = false;        // 是否激活
        [SerializeField] private bool _isCompleted = false;     // 是否完成
        [SerializeField] private bool _isUnlocked = true;       // 是否解锁
        [SerializeField] private bool _isFailed = false;        // 是否失败
        [SerializeField] private DateTime _startTime;           // 开始时间
        [SerializeField] private DateTime _completionTime;      // 完成时间
        [SerializeField] private int _completionCount = 0;      // 完成次数
        
        // 属性访问器
        public bool isActive { get => _isActive; set => _isActive = value; }
        public bool isCompleted { get => _isCompleted; set => _isCompleted = value; }
        public bool isUnlocked { get => _isUnlocked; set => _isUnlocked = value; }
        public bool isFailed { get => _isFailed; set => _isFailed = value; }
        public DateTime startTime { get => _startTime; set => _startTime = value; }
        public DateTime completionTime { get => _completionTime; set => _completionTime = value; }
        public int completionCount { get => _completionCount; set => _completionCount = value; }
        
        /// <summary>
        /// 检查是否可以开始任务
        /// </summary>
        public bool CanStart()
        {
            if (!isUnlocked || isActive || (isCompleted && !isRepeatable)) return false;
            
            var player = GameManager.Instance?.currentPlayer;
            if (player == null) return false;
            
            // 检查玩家等级
            if (player.level < minPlayerLevel || player.level > maxPlayerLevel) return false;
            
            // 检查前置任务
            foreach (var prereqId in prerequisiteQuests)
            {
                if (!player.completedQuests.Contains(prereqId)) return false;
            }
            
            // 检查需要的卡牌
            foreach (var cardId in requiredCards)
            {
                if (!player.unlockedCards.Contains(cardId)) return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 更新任务进度
        /// </summary>
        public void UpdateProgress(Player player)
        {
            if (!isActive || isCompleted) return;
            
            foreach (var objective in objectives)
            {
                objective.UpdateProgress(player);
            }
            
            // 检查是否所有目标都完成
            if (objectives.All(obj => obj.isCompleted))
            {
                isCompleted = true;
                completionTime = DateTime.Now;
                completionCount++;
            }
            
            // 检查时间限制
            if (hasTimeLimit && DateTime.Now > startTime.AddHours(timeLimitHours))
            {
                isFailed = true;
                isActive = false;
            }
        }
        
        /// <summary>
        /// 获取任务进度百分比
        /// </summary>
        public float GetProgressPercentage()
        {
            if (objectives.Count == 0) return 1f;
            
            float totalProgress = objectives.Sum(obj => obj.GetProgressPercentage());
            return totalProgress / objectives.Count;
        }
        
        /// <summary>
        /// 获取进度信息
        /// </summary>
        public string GetProgressInfo()
        {
            if (isCompleted) return "已完成";
            if (isFailed) return "已失败";
            if (!isActive) return "未激活";
            
            var info = $"{questName} ({GetProgressPercentage():P0})\n";
            foreach (var objective in objectives)
            {
                var status = objective.isCompleted ? "✓" : "○";
                info += $"{status} {objective.description} ({objective.currentValue}/{objective.targetValue})\n";
            }
            
            if (hasTimeLimit)
            {
                var remaining = startTime.AddHours(timeLimitHours) - DateTime.Now;
                if (remaining > TimeSpan.Zero)
                {
                    info += $"剩余时间: {remaining:hh\\:mm\\:ss}";
                }
            }
            
            return info;
        }
        
        /// <summary>
        /// 检查进度是否有变化
        /// </summary>
        public bool HasProgressChanged()
        {
            return objectives.Any(obj => obj.hasProgressChanged);
        }
        
        /// <summary>
        /// 重置任务状态
        /// </summary>
        public void Reset()
        {
            isActive = false;
            isCompleted = false;
            isFailed = false;
            startTime = default;
            completionTime = default;
            
            foreach (var objective in objectives)
            {
                objective.Reset();
            }
        }
        
        /// <summary>
        /// 获取剩余时间
        /// </summary>
        public TimeSpan GetRemainingTime()
        {
            if (!hasTimeLimit || !isActive) return TimeSpan.Zero;
            
            var endTime = startTime.AddHours(timeLimitHours);
            var remaining = endTime - DateTime.Now;
            
            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }
    }
    
    /// <summary>
    /// 任务目标
    /// </summary>
    [Serializable]
    public class QuestObjective
    {
        public string description;                  // 目标描述
        public ObjectiveType objectiveType;         // 目标类型
        public AttributeType attributeType;         // 属性类型
        public int targetValue;                     // 目标值
        public int currentValue;                    // 当前值
        public bool isCompleted;                    // 是否完成
        public bool hasProgressChanged;             // 进度是否有变化
        
        /// <summary>
        /// 更新目标进度
        /// </summary>
        public void UpdateProgress(Player player)
        {
            int oldValue = currentValue;
            
            switch (objectiveType)
            {
                case ObjectiveType.ReachLevel:
                    currentValue = player.level;
                    break;
                
                case ObjectiveType.ReachAttribute:
                    currentValue = player.GetTotalAttribute(attributeType);
                    break;
                
                case ObjectiveType.EquipCard:
                    currentValue = player.table.GetCards().Count(c => c.isEquipped);
                    break;
                
                case ObjectiveType.CollectCards:
                    currentValue = player.inventory.Count + player.hand.Count + player.table.Count;
                    break;
                
                case ObjectiveType.CompleteRite:
                    currentValue = player.completedQuests.Count; // 这里应该是完成的仪式数量
                    break;
                
                case ObjectiveType.WinBattles:
                    // 需要战斗系统支持
                    break;
                
                case ObjectiveType.GainExperience:
                    currentValue = player.experience;
                    break;
            }
            
            hasProgressChanged = (oldValue != currentValue);
            isCompleted = currentValue >= targetValue;
        }
        
        /// <summary>
        /// 获取进度百分比
        /// </summary>
        public float GetProgressPercentage()
        {
            if (targetValue <= 0) return 1f;
            return Mathf.Clamp01((float)currentValue / targetValue);
        }
        
        /// <summary>
        /// 重置目标
        /// </summary>
        public void Reset()
        {
            currentValue = 0;
            isCompleted = false;
            hasProgressChanged = false;
        }
    }
    
    /// <summary>
    /// 任务奖励
    /// </summary>
    [Serializable]
    public class QuestReward
    {
        public QuestRewardType rewardType;          // 奖励类型
        public AttributeType attributeType;         // 属性类型
        public int value;                           // 奖励数值
        public string description;                  // 奖励描述
    }
    
    // ==================== 枚举定义 ====================
    
    /// <summary>
    /// 任务类型
    /// </summary>
    public enum QuestType
    {
        Tutorial,           // 教程
        Main,               // 主线
        Side,               // 支线
        Daily,              // 日常
        Weekly,             // 周常
        Achievement,        // 成就
        Collection,         // 收集
        Attribute,          // 属性
        Ritual,             // 仪式
        Story               // 剧情
    }
    
    /// <summary>
    /// 任务优先级
    /// </summary>
    public enum QuestPriority
    {
        Low,                // 低
        Normal,             // 普通
        High,               // 高
        Critical            // 紧急
    }
    
    /// <summary>
    /// 目标类型
    /// </summary>
    public enum ObjectiveType
    {
        ReachLevel,         // 达到等级
        ReachAttribute,     // 达到属性值
        EquipCard,          // 装备卡牌
        CollectCards,       // 收集卡牌
        CompleteRite,       // 完成仪式
        WinBattles,         // 赢得战斗
        GainExperience,     // 获得经验
        VisitLocation,      // 访问地点
        TalkToNPC,          // 与NPC对话
        UseItem             // 使用物品
    }
    
    /// <summary>
    /// 任务奖励类型
    /// </summary>
    public enum QuestRewardType
    {
        Experience,         // 经验值
        AttributeBonus,     // 属性加成
        DrawCard,           // 抽卡
        UnlockCard,         // 解锁卡牌
        UnlockRite,         // 解锁仪式
        Healing,            // 治疗
        Currency,           // 货币
        Item,               // 物品
        Title,              // 称号
        Achievement         // 成就
    }
}
