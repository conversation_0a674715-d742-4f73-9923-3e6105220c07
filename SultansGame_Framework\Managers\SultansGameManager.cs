using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using SultansGameClone.Core;
using SultansGameClone.Events;
using SultansGameClone.Systems;

namespace SultansGameClone.Managers
{
    /// <summary>
    /// 苏丹的游戏主管理器 - 统一管理所有游戏系统
    /// 基于真实游戏架构设计的完整框架
    /// </summary>
    public class SultansGameManager : MonoBehaviour
    {
        [Header("游戏状态")]
        public GameState currentState = GameState.Loading;
        public bool isGamePaused = false;
        public bool isInitialized = false;
        
        [Header("玩家数据")]
        public Player currentPlayer;
        
        [Header("系统引用")]
        public RiteSystem riteSystem;
        public QuestSystem questSystem;
        public CardSystem cardSystem;
        public AudioManager audioManager;
        public DataManager dataManager;
        
        [Header("游戏配置")]
        public GameConfig gameConfig;
        public bool autoSave = true;
        public float autoSaveInterval = 300f; // 5分钟
        
        // 单例
        public static SultansGameManager Instance { get; private set; }
        
        // 事件
        public event Action<GameState> OnGameStateChanged;
        public event Action<Player> OnPlayerCreated;
        public event Action OnGameInitialized;
        public event Action OnGameSaved;
        public event Action OnGameLoaded;
        
        // 私有字段
        private Coroutine autoSaveCoroutine;
        private DateTime gameStartTime;
        private float totalPlayTime;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGameManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            StartCoroutine(InitializeGameSystems());
        }
        
        /// <summary>
        /// 初始化游戏管理器
        /// </summary>
        private void InitializeGameManager()
        {
            Debug.Log("🎮 初始化苏丹的游戏管理器...");
            
            gameStartTime = DateTime.Now;
            
            // 订阅系统事件
            GameEventSystem.Subscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Subscribe<GameStateEventArgs>(OnGameStateEvent);
            
            Debug.Log("✅ 游戏管理器初始化完成");
        }
        
        /// <summary>
        /// 初始化游戏系统
        /// </summary>
        private IEnumerator InitializeGameSystems()
        {
            ChangeGameState(GameState.Loading);
            
            yield return new WaitForSeconds(0.1f);
            
            // 初始化数据管理器
            if (dataManager == null)
                dataManager = FindObjectOfType<DataManager>() ?? gameObject.AddComponent<DataManager>();
            
            yield return new WaitForSeconds(0.1f);
            
            // 初始化音频管理器
            if (audioManager == null)
                audioManager = FindObjectOfType<AudioManager>() ?? gameObject.AddComponent<AudioManager>();
            
            yield return new WaitForSeconds(0.1f);
            
            // 初始化卡牌系统
            if (cardSystem == null)
                cardSystem = FindObjectOfType<CardSystem>() ?? gameObject.AddComponent<CardSystem>();
            
            yield return new WaitForSeconds(0.1f);
            
            // 初始化仪式系统
            if (riteSystem == null)
                riteSystem = FindObjectOfType<RiteSystem>() ?? gameObject.AddComponent<RiteSystem>();
            
            yield return new WaitForSeconds(0.1f);
            
            // 初始化任务系统
            if (questSystem == null)
                questSystem = FindObjectOfType<QuestSystem>() ?? gameObject.AddComponent<QuestSystem>();
            
            yield return new WaitForSeconds(0.1f);
            
            // 加载游戏配置
            LoadGameConfig();
            
            yield return new WaitForSeconds(0.1f);
            
            // 尝试加载存档
            if (dataManager.HasSaveData())
            {
                LoadGame();
            }
            else
            {
                CreateNewPlayer();
            }
            
            yield return new WaitForSeconds(0.1f);
            
            // 启动自动保存
            if (autoSave)
            {
                StartAutoSave();
            }
            
            isInitialized = true;
            OnGameInitialized?.Invoke();
            
            ChangeGameState(GameState.MainMenu);
            
            Debug.Log("🎉 所有游戏系统初始化完成!");
        }
        
        /// <summary>
        /// 加载游戏配置
        /// </summary>
        private void LoadGameConfig()
        {
            if (gameConfig == null)
            {
                gameConfig = Resources.Load<GameConfig>("GameConfig");
                if (gameConfig == null)
                {
                    Debug.LogWarning("未找到游戏配置文件，使用默认配置");
                    gameConfig = ScriptableObject.CreateInstance<GameConfig>();
                }
            }
            
            Debug.Log("📋 游戏配置加载完成");
        }
        
        /// <summary>
        /// 创建新玩家
        /// </summary>
        public void CreateNewPlayer(string playerName = "Sultan")
        {
            currentPlayer = new Player();
            currentPlayer.Initialize(playerName);
            
            OnPlayerCreated?.Invoke(currentPlayer);
            GameEventSystem.Publish(new PlayerEventArgs(currentPlayer, PlayerEventType.LevelUp));
            
            Debug.Log($"👤 创建新玩家: {playerName}");
        }
        
        /// <summary>
        /// 开始新游戏
        /// </summary>
        public void StartNewGame(string playerName = "Sultan")
        {
            CreateNewPlayer(playerName);
            
            // 开始教程任务
            if (questSystem != null)
            {
                questSystem.StartQuest(1001); // 新手引导任务
            }
            
            ChangeGameState(GameState.Playing);
            
            Debug.Log("🚀 开始新游戏");
        }
        
        /// <summary>
        /// 改变游戏状态
        /// </summary>
        public void ChangeGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            var oldState = currentState;
            currentState = newState;
            
            OnStateExit(oldState);
            OnStateEnter(newState);
            
            OnGameStateChanged?.Invoke(newState);
            GameEventSystem.Publish(new GameStateEventArgs((GameStateType)oldState, (GameStateType)newState));
            
            Debug.Log($"🔄 游戏状态变更: {oldState} → {newState}");
        }
        
        /// <summary>
        /// 状态退出处理
        /// </summary>
        private void OnStateExit(GameState state)
        {
            switch (state)
            {
                case GameState.Playing:
                    // 暂停游戏逻辑
                    break;
            }
        }
        
        /// <summary>
        /// 状态进入处理
        /// </summary>
        private void OnStateEnter(GameState state)
        {
            switch (state)
            {
                case GameState.Loading:
                    // 显示加载界面
                    break;
                
                case GameState.MainMenu:
                    // 显示主菜单
                    if (UIManager.Instance != null)
                        UIManager.Instance.ShowMainMenu();
                    break;
                
                case GameState.Playing:
                    // 开始游戏
                    if (UIManager.Instance != null)
                        UIManager.Instance.ShowGameHUD();
                    break;
                
                case GameState.Paused:
                    // 暂停游戏
                    Time.timeScale = 0f;
                    break;
                
                case GameState.GameOver:
                    // 游戏结束
                    HandleGameOver();
                    break;
            }
        }
        
        /// <summary>
        /// 暂停游戏
        /// </summary>
        public void PauseGame()
        {
            if (currentState != GameState.Playing) return;
            
            isGamePaused = true;
            ChangeGameState(GameState.Paused);
        }
        
        /// <summary>
        /// 恢复游戏
        /// </summary>
        public void ResumeGame()
        {
            if (currentState != GameState.Paused) return;
            
            isGamePaused = false;
            Time.timeScale = 1f;
            ChangeGameState(GameState.Playing);
        }
        
        /// <summary>
        /// 处理游戏结束
        /// </summary>
        private void HandleGameOver()
        {
            Time.timeScale = 1f;
            
            // 保存游戏数据
            SaveGame();
            
            Debug.Log("💀 游戏结束");
        }
        
        /// <summary>
        /// 保存游戏
        /// </summary>
        public void SaveGame()
        {
            if (dataManager != null && currentPlayer != null)
            {
                dataManager.SavePlayerData(currentPlayer);
                OnGameSaved?.Invoke();
                Debug.Log("💾 游戏已保存");
            }
        }
        
        /// <summary>
        /// 加载游戏
        /// </summary>
        public void LoadGame()
        {
            if (dataManager != null)
            {
                currentPlayer = dataManager.LoadPlayerData();
                if (currentPlayer != null)
                {
                    OnGameLoaded?.Invoke();
                    Debug.Log("📂 游戏已加载");
                }
            }
        }
        
        /// <summary>
        /// 启动自动保存
        /// </summary>
        private void StartAutoSave()
        {
            if (autoSaveCoroutine != null)
            {
                StopCoroutine(autoSaveCoroutine);
            }
            
            autoSaveCoroutine = StartCoroutine(AutoSaveCoroutine());
        }
        
        /// <summary>
        /// 自动保存协程
        /// </summary>
        private IEnumerator AutoSaveCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(autoSaveInterval);
                
                if (currentState == GameState.Playing && currentPlayer != null)
                {
                    SaveGame();
                }
            }
        }
        
        /// <summary>
        /// 下一回合
        /// </summary>
        public void NextRound()
        {
            if (currentPlayer == null) return;
            
            currentPlayer.round++;
            
            // 更新任务进度
            if (questSystem != null)
            {
                questSystem.UpdateQuestProgress();
            }
            
            // 检查仪式完成条件
            if (riteSystem != null)
            {
                riteSystem.CheckRiteCompletion();
            }
            
            GameEventSystem.Publish(new PlayerEventArgs(currentPlayer, PlayerEventType.AttributeChanged));
            
            Debug.Log($"🔄 进入第 {currentPlayer.round} 回合");
        }
        
        /// <summary>
        /// 处理玩家事件
        /// </summary>
        private void OnPlayerEvent(PlayerEventArgs args)
        {
            if (args.EventType == PlayerEventType.HealthChanged && args.Player.currentHealth <= 0)
            {
                ChangeGameState(GameState.GameOver);
            }
        }
        
        /// <summary>
        /// 处理游戏状态事件
        /// </summary>
        private void OnGameStateEvent(GameStateEventArgs args)
        {
            // 处理状态变化的额外逻辑
        }
        
        /// <summary>
        /// 获取游戏统计信息
        /// </summary>
        public GameStats GetGameStats()
        {
            return new GameStats
            {
                totalPlayTime = (DateTime.Now - gameStartTime).TotalMinutes,
                currentRound = currentPlayer?.round ?? 0,
                playerLevel = currentPlayer?.level ?? 0,
                completedQuests = questSystem?.completedQuests.Count ?? 0,
                completedRites = riteSystem?.completedRites.Count ?? 0,
                totalCards = currentPlayer?.inventory.Count ?? 0
            };
        }
        
        private void OnDestroy()
        {
            GameEventSystem.Unsubscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Unsubscribe<GameStateEventArgs>(OnGameStateEvent);
            
            if (autoSaveCoroutine != null)
            {
                StopCoroutine(autoSaveCoroutine);
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.Playing)
            {
                SaveGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.Playing)
            {
                SaveGame();
            }
        }
    }
    
    /// <summary>
    /// 游戏状态枚举
    /// </summary>
    public enum GameState
    {
        Loading,        // 加载中
        MainMenu,       // 主菜单
        Playing,        // 游戏中
        Paused,         // 暂停
        GameOver,       // 游戏结束
        Victory,        // 胜利
        Settings        // 设置
    }
    
    /// <summary>
    /// 游戏统计信息
    /// </summary>
    [Serializable]
    public class GameStats
    {
        public double totalPlayTime;        // 总游戏时间（分钟）
        public int currentRound;            // 当前回合
        public int playerLevel;             // 玩家等级
        public int completedQuests;         // 完成的任务数
        public int completedRites;          // 完成的仪式数
        public int totalCards;              // 总卡牌数
    }
}
