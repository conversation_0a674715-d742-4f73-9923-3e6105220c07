using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

namespace SultansGameClone.UI
{
    /// <summary>
    /// 卡牌发光边框效果 - 点击时的视觉反馈
    /// </summary>
    public class CardGlowEffect : MonoBehaviour
    {
        [Header("发光边框设置")]
        public Image glowBorder;                    // 发光边框图片
        public Color glowColor = Color.yellow;      // 发光颜色
        public float glowIntensity = 2f;            // 发光强度
        public float glowDuration = 0.5f;           // 发光持续时间
        
        [Header("动画设置")]
        public float scaleMultiplier = 1.1f;        // 缩放倍数
        public float scaleDuration = 0.2f;          // 缩放动画时间
        public Ease scaleEase = Ease.OutBack;       // 缩放缓动
        
        [Header("边框动画")]
        public float borderFadeInTime = 0.1f;       // 边框淡入时间
        public float borderFadeOutTime = 0.3f;      // 边框淡出时间
        public float borderPulseSpeed = 2f;         // 边框脉冲速度
        
        [Header("粒子效果")]
        public ParticleSystem glowParticles;        // 发光粒子
        public bool enableParticles = true;         // 是否启用粒子
        
        [Header("音效")]
        public AudioClip selectSound;               // 选中音效
        public AudioClip hoverSound;                // 悬停音效
        
        // 私有变量
        private RectTransform rectTransform;
        private CanvasGroup canvasGroup;
        private Vector3 originalScale;
        private Color originalGlowColor;
        private bool isGlowing = false;
        private bool isSelected = false;
        private Coroutine glowCoroutine;
        private Coroutine pulseCoroutine;
        
        // 材质相关
        private Material glowMaterial;
        private Image cardImage;
        
        private void Awake()
        {
            InitializeComponents();
            SetupGlowBorder();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            rectTransform = GetComponent<RectTransform>();
            canvasGroup = GetComponent<CanvasGroup>();
            cardImage = GetComponent<Image>();
            
            if (canvasGroup == null)
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            
            originalScale = rectTransform.localScale;
            
            // 创建发光边框（如果不存在）
            if (glowBorder == null)
            {
                CreateGlowBorder();
            }
            
            originalGlowColor = glowColor;
        }
        
        /// <summary>
        /// 创建发光边框
        /// </summary>
        private void CreateGlowBorder()
        {
            // 创建边框GameObject
            var borderGO = new GameObject("GlowBorder");
            borderGO.transform.SetParent(transform, false);
            
            // 添加Image组件
            glowBorder = borderGO.AddComponent<Image>();
            
            // 设置边框图片（使用默认的UI精灵或自定义边框）
            glowBorder.sprite = Resources.Load<Sprite>("UI/CardBorder");
            if (glowBorder.sprite == null)
            {
                // 如果没有找到边框精灵，使用默认的UI精灵
                glowBorder.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
            }
            
            // 设置边框属性
            glowBorder.type = Image.Type.Sliced;
            glowBorder.color = new Color(glowColor.r, glowColor.g, glowColor.b, 0f);
            
            // 设置RectTransform
            var borderRect = glowBorder.GetComponent<RectTransform>();
            borderRect.anchorMin = Vector2.zero;
            borderRect.anchorMax = Vector2.one;
            borderRect.offsetMin = Vector2.one * -10f; // 边框向外扩展10像素
            borderRect.offsetMax = Vector2.one * 10f;
            
            // 确保边框在最上层
            borderGO.transform.SetAsLastSibling();
        }
        
        /// <summary>
        /// 设置发光边框
        /// </summary>
        private void SetupGlowBorder()
        {
            if (glowBorder != null)
            {
                // 创建发光材质
                CreateGlowMaterial();
                
                // 初始化边框透明度
                var color = glowBorder.color;
                color.a = 0f;
                glowBorder.color = color;
            }
        }
        
        /// <summary>
        /// 创建发光材质
        /// </summary>
        private void CreateGlowMaterial()
        {
            // 尝试加载自定义发光材质
            glowMaterial = Resources.Load<Material>("Materials/CardGlow");
            
            if (glowMaterial == null)
            {
                // 如果没有自定义材质，创建一个简单的发光效果
                glowMaterial = new Material(Shader.Find("UI/Default"));
            }
            
            if (glowBorder != null)
            {
                glowBorder.material = glowMaterial;
            }
        }
        
        /// <summary>
        /// 开始发光效果
        /// </summary>
        public void StartGlow()
        {
            if (isGlowing) return;
            
            isGlowing = true;
            
            // 停止之前的协程
            if (glowCoroutine != null)
                StopCoroutine(glowCoroutine);
            
            glowCoroutine = StartCoroutine(GlowAnimation());
            
            // 播放音效
            PlaySelectSound();
            
            // 启动粒子效果
            if (enableParticles && glowParticles != null)
            {
                glowParticles.Play();
            }
        }
        
        /// <summary>
        /// 停止发光效果
        /// </summary>
        public void StopGlow()
        {
            if (!isGlowing) return;
            
            isGlowing = false;
            
            if (glowCoroutine != null)
            {
                StopCoroutine(glowCoroutine);
                glowCoroutine = null;
            }
            
            // 淡出边框
            if (glowBorder != null)
            {
                glowBorder.DOFade(0f, borderFadeOutTime);
            }
            
            // 恢复原始缩放
            rectTransform.DOScale(originalScale, scaleDuration).SetEase(scaleEase);
            
            // 停止粒子效果
            if (glowParticles != null)
            {
                glowParticles.Stop();
            }
        }
        
        /// <summary>
        /// 设置选中状态
        /// </summary>
        public void SetSelected(bool selected)
        {
            isSelected = selected;
            
            if (selected)
            {
                StartGlow();
                StartPulseEffect();
            }
            else
            {
                StopGlow();
                StopPulseEffect();
            }
        }
        
        /// <summary>
        /// 开始脉冲效果（选中状态下持续的效果）
        /// </summary>
        private void StartPulseEffect()
        {
            if (pulseCoroutine != null)
                StopCoroutine(pulseCoroutine);
            
            pulseCoroutine = StartCoroutine(PulseAnimation());
        }
        
        /// <summary>
        /// 停止脉冲效果
        /// </summary>
        private void StopPulseEffect()
        {
            if (pulseCoroutine != null)
            {
                StopCoroutine(pulseCoroutine);
                pulseCoroutine = null;
            }
        }
        
        /// <summary>
        /// 发光动画协程
        /// </summary>
        private IEnumerator GlowAnimation()
        {
            // 缩放动画
            rectTransform.DOScale(originalScale * scaleMultiplier, scaleDuration).SetEase(scaleEase);
            
            // 边框淡入
            if (glowBorder != null)
            {
                glowBorder.DOFade(glowIntensity, borderFadeInTime);
            }
            
            // 等待发光持续时间
            yield return new WaitForSeconds(glowDuration);
            
            // 如果不是选中状态，则停止发光
            if (!isSelected)
            {
                StopGlow();
            }
        }
        
        /// <summary>
        /// 脉冲动画协程
        /// </summary>
        private IEnumerator PulseAnimation()
        {
            while (isSelected && glowBorder != null)
            {
                // 脉冲效果
                float pulseAlpha = (Mathf.Sin(Time.time * borderPulseSpeed) + 1f) * 0.5f;
                pulseAlpha = Mathf.Lerp(0.3f, glowIntensity, pulseAlpha);
                
                var color = glowBorder.color;
                color.a = pulseAlpha;
                glowBorder.color = color;
                
                yield return null;
            }
        }
        
        /// <summary>
        /// 悬停效果
        /// </summary>
        public void OnHover()
        {
            if (isSelected) return;
            
            // 轻微的缩放效果
            rectTransform.DOScale(originalScale * 1.05f, 0.1f).SetEase(Ease.OutQuad);
            
            // 轻微的边框发光
            if (glowBorder != null)
            {
                glowBorder.DOFade(0.3f, 0.1f);
            }
            
            // 播放悬停音效
            PlayHoverSound();
        }
        
        /// <summary>
        /// 取消悬停效果
        /// </summary>
        public void OnHoverExit()
        {
            if (isSelected) return;
            
            // 恢复原始缩放
            rectTransform.DOScale(originalScale, 0.1f).SetEase(Ease.OutQuad);
            
            // 隐藏边框
            if (glowBorder != null)
            {
                glowBorder.DOFade(0f, 0.2f);
            }
        }
        
        /// <summary>
        /// 播放选中音效
        /// </summary>
        private void PlaySelectSound()
        {
            if (selectSound != null && SultansGameClone.Managers.AudioManager.Instance != null)
            {
                SultansGameClone.Managers.AudioManager.Instance.PlaySFX("card_select");
            }
        }
        
        /// <summary>
        /// 播放悬停音效
        /// </summary>
        private void PlayHoverSound()
        {
            if (hoverSound != null && SultansGameClone.Managers.AudioManager.Instance != null)
            {
                SultansGameClone.Managers.AudioManager.Instance.PlaySFX("card_hover");
            }
        }
        
        /// <summary>
        /// 设置发光颜色
        /// </summary>
        public void SetGlowColor(Color color)
        {
            glowColor = color;
            if (glowBorder != null)
            {
                var borderColor = glowBorder.color;
                borderColor.r = color.r;
                borderColor.g = color.g;
                borderColor.b = color.b;
                glowBorder.color = borderColor;
            }
        }
        
        /// <summary>
        /// 设置发光强度
        /// </summary>
        public void SetGlowIntensity(float intensity)
        {
            glowIntensity = intensity;
        }
        
        /// <summary>
        /// 立即发光效果（用于快速反馈）
        /// </summary>
        public void FlashGlow()
        {
            if (glowBorder != null)
            {
                glowBorder.DOFade(glowIntensity, 0.05f).OnComplete(() =>
                {
                    glowBorder.DOFade(0f, 0.2f);
                });
            }
        }
        
        private void OnDestroy()
        {
            // 清理DOTween动画
            rectTransform?.DOKill();
            glowBorder?.DOKill();
            
            // 停止协程
            if (glowCoroutine != null)
                StopCoroutine(glowCoroutine);
            if (pulseCoroutine != null)
                StopCoroutine(pulseCoroutine);
        }
    }
}
