using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace SultansGameClone
{
    // ==================== 拖拽放置区域 ====================
    
    public class DropZone : MonoBeh<PERSON>our, IDropHandler, IPointerEnterHandler, IPointerExitHandler
    {
        [Header("放置区域设置")]
        public DropZoneType zoneType;
        public int maxCapacity = -1; // -1表示无限制
        public CardType[] acceptedCardTypes; // 接受的卡牌类型
        public bool requiresSpecificTags = false;
        public string[] requiredTags; // 需要的标签
        
        [Header("视觉反馈")]
        public Image backgroundImage;
        public Color normalColor = Color.white;
        public Color highlightColor = Color.green;
        public Color invalidColor = Color.red;
        public GameObject dropIndicator;
        
        [Header("音效")]
        public AudioClip dropSuccessSound;
        public AudioClip dropFailSound;
        
        // 私有字段
        private bool isHighlighted = false;
        private AudioSource audioSource;
        
        // 对应的卡牌集合
        public CardCollection targetCollection { get; private set; }
        
        private void Awake()
        {
            // 获取音频源
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
            
            // 初始化视觉状态
            if (backgroundImage == null)
                backgroundImage = GetComponent<Image>();
            
            if (dropIndicator != null)
                dropIndicator.SetActive(false);
            
            // 设置目标集合
            SetupTargetCollection();
        }
        
        // 设置目标集合
        private void SetupTargetCollection()
        {
            if (GameManager.Instance == null) return;
            
            switch (zoneType)
            {
                case DropZoneType.Hand:
                    targetCollection = GameManager.Instance.hand;
                    break;
                case DropZoneType.Table:
                    targetCollection = GameManager.Instance.table;
                    break;
                case DropZoneType.Inventory:
                    targetCollection = GameManager.Instance.inventory;
                    break;
                case DropZoneType.Deck:
                    targetCollection = GameManager.Instance.deck;
                    break;
            }
        }
        
        // 检查是否可以接受卡牌
        public bool CanAcceptCard(Card card)
        {
            if (card == null || targetCollection == null) return false;
            
            // 检查容量限制
            if (!targetCollection.CanAddCard())
            {
                Debug.LogWarning($"⚠️ 放置区域已满: {zoneType}");
                return false;
            }
            
            // 检查卡牌类型
            if (acceptedCardTypes != null && acceptedCardTypes.Length > 0)
            {
                bool typeAccepted = false;
                foreach (var acceptedType in acceptedCardTypes)
                {
                    if (card.type == acceptedType)
                    {
                        typeAccepted = true;
                        break;
                    }
                }
                
                if (!typeAccepted)
                {
                    Debug.LogWarning($"⚠️ 卡牌类型不被接受: {card.type} in {zoneType}");
                    return false;
                }
            }
            
            // 检查标签要求
            if (requiresSpecificTags && requiredTags != null && requiredTags.Length > 0)
            {
                bool hasRequiredTag = false;
                foreach (var requiredTag in requiredTags)
                {
                    if (card.tags.Contains(requiredTag))
                    {
                        hasRequiredTag = true;
                        break;
                    }
                }
                
                if (!hasRequiredTag)
                {
                    Debug.LogWarning($"⚠️ 卡牌缺少必需标签: {string.Join(",", requiredTags)}");
                    return false;
                }
            }
            
            // 检查特殊规则
            return CheckSpecialRules(card);
        }
        
        // 检查特殊规则
        private bool CheckSpecialRules(Card card)
        {
            switch (zoneType)
            {
                case DropZoneType.Table:
                    // 桌面特殊规则：不能放置已装备的卡牌
                    if (card.isEquipped && GameManager.Instance.table.cards.Contains(card))
                    {
                        return false;
                    }
                    break;
                    
                case DropZoneType.Hand:
                    // 手牌特殊规则：不能放置锁定的卡牌
                    if (card.isLocked)
                    {
                        return false;
                    }
                    break;
            }
            
            return true;
        }
        
        // 接受卡牌
        public bool AcceptCard(Card card)
        {
            if (!CanAcceptCard(card)) 
            {
                PlayDropFailEffect();
                return false;
            }
            
            // 找到卡牌的源集合
            CardCollection sourceCollection = FindCardSource(card);
            if (sourceCollection == null)
            {
                Debug.LogError($"❌ 找不到卡牌源集合: {card.name}");
                PlayDropFailEffect();
                return false;
            }
            
            // 执行移动
            bool success = GameManager.Instance.MoveCard(card, sourceCollection, targetCollection);
            
            if (success)
            {
                PlayDropSuccessEffect();
                Debug.Log($"✅ 卡牌成功放置: {card.name} -> {zoneType}");
            }
            else
            {
                PlayDropFailEffect();
                Debug.LogError($"❌ 卡牌放置失败: {card.name} -> {zoneType}");
            }
            
            return success;
        }
        
        // 查找卡牌源集合
        private CardCollection FindCardSource(Card card)
        {
            if (GameManager.Instance == null) return null;
            
            var collections = new CardCollection[]
            {
                GameManager.Instance.hand,
                GameManager.Instance.table,
                GameManager.Instance.inventory,
                GameManager.Instance.deck
            };
            
            foreach (var collection in collections)
            {
                if (collection != null && collection.cards.Contains(card))
                {
                    return collection;
                }
            }
            
            return null;
        }
        
        // ==================== 事件处理 ====================
        
        // 拖拽放置事件
        public void OnDrop(PointerEventData eventData)
        {
            // 获取被拖拽的卡牌
            GameObject draggedObject = eventData.pointerDrag;
            if (draggedObject == null) return;
            
            SultansCardController cardController = draggedObject.GetComponent<SultansCardController>();
            if (cardController == null) return;
            
            Card card = cardController.GetCard();
            if (card == null) return;
            
            // 尝试接受卡牌
            AcceptCard(card);
            
            // 取消高亮
            SetHighlight(false);
        }
        
        // 指针进入事件
        public void OnPointerEnter(PointerEventData eventData)
        {
            // 检查是否有卡牌被拖拽
            if (eventData.pointerDrag != null)
            {
                SultansCardController cardController = eventData.pointerDrag.GetComponent<SultansCardController>();
                if (cardController != null)
                {
                    Card card = cardController.GetCard();
                    bool canAccept = CanAcceptCard(card);
                    SetHighlight(true, canAccept);
                }
            }
        }
        
        // 指针离开事件
        public void OnPointerExit(PointerEventData eventData)
        {
            SetHighlight(false);
        }
        
        // ==================== 视觉反馈 ====================
        
        // 设置高亮状态
        private void SetHighlight(bool highlight, bool valid = true)
        {
            isHighlighted = highlight;
            
            if (backgroundImage != null)
            {
                Color targetColor = normalColor;
                if (highlight)
                {
                    targetColor = valid ? highlightColor : invalidColor;
                }
                backgroundImage.color = targetColor;
            }
            
            if (dropIndicator != null)
            {
                dropIndicator.SetActive(highlight && valid);
            }
        }
        
        // 播放成功放置效果
        private void PlayDropSuccessEffect()
        {
            // 播放音效
            if (audioSource != null && dropSuccessSound != null)
            {
                audioSource.PlayOneShot(dropSuccessSound);
            }
            
            // 播放视觉效果
            if (backgroundImage != null)
            {
                Color originalColor = backgroundImage.color;
                backgroundImage.color = highlightColor;
                
                // 使用DOTween创建闪烁效果
                backgroundImage.color = highlightColor;
                // 这里可以添加DOTween动画，如果项目中有DOTween的话
            }
        }
        
        // 播放失败放置效果
        private void PlayDropFailEffect()
        {
            // 播放音效
            if (audioSource != null && dropFailSound != null)
            {
                audioSource.PlayOneShot(dropFailSound);
            }
            
            // 播放视觉效果
            if (backgroundImage != null)
            {
                Color originalColor = backgroundImage.color;
                backgroundImage.color = invalidColor;
                
                // 这里可以添加摇晃动画
            }
        }
        
        // 获取当前卡牌数量
        public int GetCurrentCardCount()
        {
            return targetCollection?.cards.Count ?? 0;
        }
        
        // 获取最大容量
        public int GetMaxCapacity()
        {
            return targetCollection?.maxCapacity ?? maxCapacity;
        }
        
        // 检查是否已满
        public bool IsFull()
        {
            if (targetCollection == null) return true;
            return !targetCollection.CanAddCard();
        }
    }
    
    // 放置区域类型枚举
    public enum DropZoneType
    {
        Hand,       // 手牌区域
        Table,      // 桌面区域
        Inventory,  // 库存区域
        Deck        // 卡牌库区域
    }
}
