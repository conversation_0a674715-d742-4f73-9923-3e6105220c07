# 苏丹的游戏框架 - 快速开始指南

## 🚀 5分钟快速设置

### 步骤1: 导入框架
1. 将整个 `SultansGame_Framework` 文件夹复制到Unity项目的 `Assets` 目录下
2. 等待Unity编译完成

### 步骤2: 创建游戏场景
1. 创建新场景 `SultansGameScene`
2. 创建空的GameObject，命名为 `GameManager`
3. 添加 `SultansGameManager` 组件到 `GameManager`
4. 添加 `GameExample` 组件到 `GameManager`（可选，用于演示）

### 步骤3: 设置UI管理器
1. 创建Canvas，命名为 `GameUI`
2. 创建空的GameObject作为Canvas子对象，命名为 `UIManager`
3. 添加 `UIManager` 组件
4. 在UIManager中设置各种UI面板的引用

### 步骤4: 运行游戏
1. 点击Play按钮
2. 观察Console输出，查看系统初始化过程
3. 如果添加了GameExample组件，会自动运行演示

## 🎮 基础使用示例

### 创建玩家和卡牌
```csharp
// 获取游戏管理器
var gameManager = SultansGameManager.Instance;

// 创建新玩家
gameManager.CreateNewPlayer("我的角色");

// 获取卡牌系统
var cardSystem = CardSystem.Instance;

// 创建随机卡牌
var card = cardSystem.CreateRandomCard();

// 添加到玩家手牌
gameManager.currentPlayer.AddCardToHand(card);
```

### 开始仪式
```csharp
// 获取仪式系统
var riteSystem = RiteSystem.Instance;

// 开始仪式（使用预定义的仪式ID）
riteSystem.StartRite(3300001); // 力量仪式
```

### 开始任务
```csharp
// 获取任务系统
var questSystem = QuestSystem.Instance;

// 开始教程任务
questSystem.StartQuest(1001); // 初入宫廷
```

### 事件监听
```csharp
// 订阅卡牌事件
GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);

// 事件处理方法
private void OnCardEvent(CardEventArgs args)
{
    Debug.Log($"卡牌事件: {args.Card.name} - {args.EventType}");
}
```

## 🔧 必要的配置

### 1. 创建游戏配置文件
1. 在Project窗口右键
2. 选择 `Create > Sultan's Game > Game Config`
3. 命名为 `GameConfig`
4. 放置在 `Resources` 文件夹中

### 2. 创建卡牌数据库
1. 在Project窗口右键
2. 选择 `Create > Sultan's Game > Card Database`
3. 命名为 `CardDatabase`
4. 放置在 `Resources` 文件夹中
5. 添加卡牌配置

### 3. 创建音频数据库
1. 在Project窗口右键
2. 选择 `Create > Sultan's Game > Audio Database`
3. 命名为 `AudioDatabase`
4. 放置在 `Resources` 文件夹中
5. 添加音频剪辑

## 📁 推荐的项目结构
```
Assets/
├── SultansGame_Framework/     # 框架代码
├── Resources/                 # 资源文件
│   ├── GameConfig.asset      # 游戏配置
│   ├── CardDatabase.asset    # 卡牌数据库
│   └── AudioDatabase.asset   # 音频数据库
├── Scenes/                   # 场景文件
│   └── SultansGameScene.unity
├── Prefabs/                  # 预制体
│   ├── CardPrefab.prefab
│   └── UIPanels/
└── Audio/                    # 音频文件
    ├── Music/
    ├── SFX/
    └── Voice/
```

## 🎯 常见问题

### Q: 编译错误 "找不到类型"
A: 确保所有脚本都在正确的命名空间中，并且using语句正确

### Q: 游戏管理器初始化失败
A: 检查是否正确添加了SultansGameManager组件，并且场景中只有一个实例

### Q: 卡牌系统无法创建卡牌
A: 确保创建了CardDatabase并放置在Resources文件夹中

### Q: 音频无法播放
A: 检查AudioDatabase配置和音频文件路径

### Q: 事件系统不工作
A: 确保正确订阅了事件，并且在OnDestroy中取消订阅

## 🔍 调试技巧

### 1. 启用调试模式
```csharp
// 在GameConfig中设置
gameConfig.debugMode = true;
gameConfig.showDebugInfo = true;
```

### 2. 使用GameExample组件
- 添加GameExample组件到GameManager
- 启用autoStartGame选项
- 观察Console输出了解系统运行状态

### 3. 手动测试功能
- 在GameExample组件的Inspector中
- 使用右键菜单的"手动测试"选项
- 或调用ShowCurrentStatus查看当前状态

## 📚 下一步

1. **阅读完整文档**: 查看 `README.md` 了解详细功能
2. **自定义卡牌**: 创建自己的卡牌配置
3. **设计仪式**: 添加新的仪式类型
4. **扩展任务**: 创建自定义任务目标
5. **美化UI**: 设计游戏界面
6. **添加音效**: 配置音频资源

## 🆘 获取帮助

如果遇到问题：
1. 检查Console错误信息
2. 确认所有依赖项已正确配置
3. 参考GameExample.cs中的示例代码
4. 查看各个系统的详细文档

---

**祝您使用愉快！** 🎉
