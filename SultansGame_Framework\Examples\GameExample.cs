using System.Collections;
using UnityEngine;
using SultansGameClone.Core;
using SultansGameClone.Events;
using SultansGameClone.Systems;
using SultansGameClone.Managers;

namespace SultansGameClone.Examples
{
    /// <summary>
    /// 游戏示例 - 演示如何使用苏丹的游戏框架
    /// </summary>
    public class GameExample : MonoBehaviour
    {
        [Header("示例设置")]
        public bool autoStartGame = true;
        public bool showDebugInfo = true;
        public string playerName = "测试玩家";
        
        [Header("测试功能")]
        public bool testCardSystem = true;
        public bool testRiteSystem = true;
        public bool testQuestSystem = true;
        public bool testEventSystem = true;
        
        private SultansGameManager gameManager;
        private Player testPlayer;
        
        private void Start()
        {
            if (autoStartGame)
            {
                StartCoroutine(RunGameExample());
            }
        }
        
        /// <summary>
        /// 运行游戏示例
        /// </summary>
        private IEnumerator RunGameExample()
        {
            Debug.Log("🎮 开始苏丹的游戏框架演示");
            
            // 等待游戏管理器初始化
            yield return new WaitUntil(() => SultansGameManager.Instance != null && SultansGameManager.Instance.isInitialized);
            
            gameManager = SultansGameManager.Instance;
            
            // 订阅事件
            if (testEventSystem)
            {
                SubscribeToEvents();
            }
            
            yield return new WaitForSeconds(1f);
            
            // 创建测试玩家
            CreateTestPlayer();
            
            yield return new WaitForSeconds(1f);
            
            // 测试卡牌系统
            if (testCardSystem)
            {
                yield return StartCoroutine(TestCardSystem());
            }
            
            yield return new WaitForSeconds(2f);
            
            // 测试仪式系统
            if (testRiteSystem)
            {
                yield return StartCoroutine(TestRiteSystem());
            }
            
            yield return new WaitForSeconds(2f);
            
            // 测试任务系统
            if (testQuestSystem)
            {
                yield return StartCoroutine(TestQuestSystem());
            }
            
            yield return new WaitForSeconds(2f);
            
            // 显示最终状态
            ShowFinalStatus();
            
            Debug.Log("✅ 苏丹的游戏框架演示完成");
        }
        
        /// <summary>
        /// 创建测试玩家
        /// </summary>
        private void CreateTestPlayer()
        {
            Debug.Log("👤 创建测试玩家...");
            
            gameManager.CreateNewPlayer(playerName);
            testPlayer = gameManager.currentPlayer;
            
            if (showDebugInfo)
            {
                Debug.Log($"玩家创建成功: {testPlayer.playerName}");
                Debug.Log($"初始属性 - 体魄:{testPlayer.basePhysique} 魅力:{testPlayer.baseCharm} " +
                         $"智慧:{testPlayer.baseIntelligence} 财富:{testPlayer.baseFortune} 精神:{testPlayer.baseSpirit}");
            }
        }
        
        /// <summary>
        /// 测试卡牌系统
        /// </summary>
        private IEnumerator TestCardSystem()
        {
            Debug.Log("🃏 测试卡牌系统...");
            
            var cardSystem = CardSystem.Instance;
            if (cardSystem == null)
            {
                Debug.LogError("卡牌系统未初始化");
                yield break;
            }
            
            // 创建一些测试卡牌
            for (int i = 0; i < 5; i++)
            {
                var card = cardSystem.CreateRandomCard();
                if (card != null)
                {
                    testPlayer.AddCardToHand(card);
                    
                    if (showDebugInfo)
                    {
                        Debug.Log($"创建卡牌: {card.name} ({card.rarity}) - " +
                                 $"属性总和: {card.GetTotalAttributes()}");
                    }
                }
                
                yield return new WaitForSeconds(0.2f);
            }
            
            // 装备一张卡牌
            if (testPlayer.hand.Count > 0)
            {
                var cardToEquip = testPlayer.hand.GetCards()[0];
                testPlayer.EquipCard(cardToEquip);
                
                if (showDebugInfo)
                {
                    Debug.Log($"装备卡牌: {cardToEquip.name}");
                    Debug.Log($"装备后总属性 - 体魄:{testPlayer.GetTotalAttribute(AttributeType.Physique)} " +
                             $"魅力:{testPlayer.GetTotalAttribute(AttributeType.Charm)} " +
                             $"智慧:{testPlayer.GetTotalAttribute(AttributeType.Intelligence)} " +
                             $"财富:{testPlayer.GetTotalAttribute(AttributeType.Fortune)} " +
                             $"精神:{testPlayer.GetTotalAttribute(AttributeType.Spirit)}");
                }
            }
            
            // 测试卡牌升级
            if (testPlayer.hand.Count > 1)
            {
                var cardToUpgrade = testPlayer.hand.GetCards()[1];
                cardSystem.UpgradeCard(cardToUpgrade, 100);
                
                if (showDebugInfo)
                {
                    Debug.Log($"升级卡牌: {cardToUpgrade.name} 到等级 {cardToUpgrade.level}");
                }
            }
        }
        
        /// <summary>
        /// 测试仪式系统
        /// </summary>
        private IEnumerator TestRiteSystem()
        {
            Debug.Log("🔮 测试仪式系统...");
            
            var riteSystem = RiteSystem.Instance;
            if (riteSystem == null)
            {
                Debug.LogError("仪式系统未初始化");
                yield break;
            }
            
            // 获取可用仪式
            var availableRites = riteSystem.GetAvailableRites();
            
            if (showDebugInfo)
            {
                Debug.Log($"可用仪式数量: {availableRites.Count}");
                foreach (var rite in availableRites)
                {
                    Debug.Log($"仪式: {rite.name} - 难度: {rite.difficulty}");
                }
            }
            
            // 尝试开始第一个仪式
            if (availableRites.Count > 0)
            {
                var firstRite = availableRites[0];
                bool started = riteSystem.StartRite(firstRite.id);
                
                if (started)
                {
                    Debug.Log($"开始仪式: {firstRite.name}");
                    
                    // 等待一段时间后检查完成条件
                    yield return new WaitForSeconds(1f);
                    riteSystem.CheckRiteCompletion();
                    
                    if (showDebugInfo)
                    {
                        var progress = firstRite.GetProgress(testPlayer);
                        Debug.Log($"仪式进度: {progress:P0}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 测试任务系统
        /// </summary>
        private IEnumerator TestQuestSystem()
        {
            Debug.Log("📜 测试任务系统...");
            
            var questSystem = QuestSystem.Instance;
            if (questSystem == null)
            {
                Debug.LogError("任务系统未初始化");
                yield break;
            }
            
            // 开始教程任务
            bool questStarted = questSystem.StartQuest(1001);
            
            if (questStarted)
            {
                Debug.Log("开始教程任务: 初入宫廷");
                
                // 模拟完成任务目标
                yield return new WaitForSeconds(1f);
                
                // 更新任务进度
                questSystem.UpdateQuestProgress();
                
                if (showDebugInfo)
                {
                    var progressInfo = questSystem.GetQuestProgressInfo(1001);
                    Debug.Log($"任务进度:\n{progressInfo}");
                }
            }
            
            // 显示活跃任务
            if (showDebugInfo)
            {
                Debug.Log($"活跃任务数量: {questSystem.activeQuests.Count}");
                Debug.Log($"已完成任务数量: {questSystem.completedQuests.Count}");
            }
        }
        
        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            Debug.Log("📡 订阅游戏事件...");
            
            GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Subscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Subscribe<RiteEventArgs>(OnRiteEvent);
            GameEventSystem.Subscribe<QuestEventArgs>(OnQuestEvent);
            GameEventSystem.Subscribe<GameStateEventArgs>(OnGameStateEvent);
        }
        
        /// <summary>
        /// 显示最终状态
        /// </summary>
        private void ShowFinalStatus()
        {
            if (!showDebugInfo || testPlayer == null) return;
            
            Debug.Log("📊 最终游戏状态:");
            Debug.Log($"玩家: {testPlayer.playerName} (等级 {testPlayer.level})");
            Debug.Log($"生命值: {testPlayer.currentHealth}/{testPlayer.maxHealth}");
            Debug.Log($"手牌数量: {testPlayer.hand.Count}");
            Debug.Log($"装备数量: {testPlayer.table.Count}");
            Debug.Log($"总属性: 体魄{testPlayer.GetTotalAttribute(AttributeType.Physique)} " +
                     $"魅力{testPlayer.GetTotalAttribute(AttributeType.Charm)} " +
                     $"智慧{testPlayer.GetTotalAttribute(AttributeType.Intelligence)} " +
                     $"财富{testPlayer.GetTotalAttribute(AttributeType.Fortune)} " +
                     $"精神{testPlayer.GetTotalAttribute(AttributeType.Spirit)}");
            
            var gameStats = gameManager.GetGameStats();
            Debug.Log($"游戏统计: 回合{gameStats.currentRound} 完成任务{gameStats.completedQuests} " +
                     $"完成仪式{gameStats.completedRites} 总卡牌{gameStats.totalCards}");
        }
        
        // ==================== 事件处理 ====================
        
        private void OnCardEvent(CardEventArgs args)
        {
            if (showDebugInfo)
            {
                Debug.Log($"🃏 卡牌事件: {args.Card?.name} - {args.EventType}");
            }
        }
        
        private void OnPlayerEvent(PlayerEventArgs args)
        {
            if (showDebugInfo)
            {
                Debug.Log($"👤 玩家事件: {args.Player?.playerName} - {args.EventType}");
            }
        }
        
        private void OnRiteEvent(RiteEventArgs args)
        {
            if (showDebugInfo)
            {
                Debug.Log($"🔮 仪式事件: ID{args.RiteId} - {args.EventType} - 成功:{args.IsSuccessful}");
            }
        }
        
        private void OnQuestEvent(QuestEventArgs args)
        {
            if (showDebugInfo)
            {
                Debug.Log($"📜 任务事件: {args.QuestName} - {args.EventType}");
            }
        }
        
        private void OnGameStateEvent(GameStateEventArgs args)
        {
            if (showDebugInfo)
            {
                Debug.Log($"🎮 游戏状态变化: {args.OldState} → {args.NewState}");
            }
        }
        
        // ==================== 手动测试方法 ====================
        
        [ContextMenu("手动测试卡牌系统")]
        public void ManualTestCardSystem()
        {
            StartCoroutine(TestCardSystem());
        }
        
        [ContextMenu("手动测试仪式系统")]
        public void ManualTestRiteSystem()
        {
            StartCoroutine(TestRiteSystem());
        }
        
        [ContextMenu("手动测试任务系统")]
        public void ManualTestQuestSystem()
        {
            StartCoroutine(TestQuestSystem());
        }
        
        [ContextMenu("显示当前状态")]
        public void ShowCurrentStatus()
        {
            ShowFinalStatus();
        }
        
        [ContextMenu("保存游戏")]
        public void SaveGame()
        {
            if (gameManager != null)
            {
                gameManager.SaveGame();
                Debug.Log("💾 游戏已保存");
            }
        }
        
        [ContextMenu("加载游戏")]
        public void LoadGame()
        {
            if (gameManager != null)
            {
                gameManager.LoadGame();
                testPlayer = gameManager.currentPlayer;
                Debug.Log("📂 游戏已加载");
            }
        }
        
        private void OnDestroy()
        {
            // 取消事件订阅
            if (testEventSystem)
            {
                GameEventSystem.Unsubscribe<CardEventArgs>(OnCardEvent);
                GameEventSystem.Unsubscribe<PlayerEventArgs>(OnPlayerEvent);
                GameEventSystem.Unsubscribe<RiteEventArgs>(OnRiteEvent);
                GameEventSystem.Unsubscribe<QuestEventArgs>(OnQuestEvent);
                GameEventSystem.Unsubscribe<GameStateEventArgs>(OnGameStateEvent);
            }
        }
    }
}
