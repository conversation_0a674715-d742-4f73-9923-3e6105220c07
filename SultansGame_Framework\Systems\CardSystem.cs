using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using SultansGameClone.Core;
using SultansGameClone.Events;

namespace SultansGameClone.Systems
{
    /// <summary>
    /// 卡牌系统 - 管理卡牌的创建、配置和行为
    /// </summary>
    public class CardSystem : MonoBehaviour
    {
        [Header("卡牌配置")]
        public List<CardConfig> cardConfigs = new List<CardConfig>();
        public CardDatabase cardDatabase;
        
        [Header("卡牌生成")]
        public int initialDeckSize = 20;
        public int maxHandSize = 10;
        public int maxTableSize = 5;
        
        [Header("稀有度权重")]
        public float commonWeight = 60f;
        public float uncommonWeight = 25f;
        public float rareWeight = 10f;
        public float epicWeight = 4f;
        public float legendaryWeight = 1f;
        
        // 单例
        public static CardSystem Instance { get; private set; }
        
        // 卡牌缓存
        private Dictionary<int, Card> cardCache = new Dictionary<int, Card>();
        private Dictionary<CardRarity, List<CardConfig>> cardsByRarity = new Dictionary<CardRarity, List<CardConfig>>();
        
        // 事件
        public event Action<Card> OnCardCreated;
        public event Action<Card> OnCardDestroyed;
        public event Action<Card, Player> OnCardUsed;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeCardSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 初始化卡牌系统
        /// </summary>
        private void InitializeCardSystem()
        {
            Debug.Log("🃏 初始化卡牌系统...");
            
            // 加载卡牌数据
            LoadCardDatabase();
            
            // 按稀有度分类卡牌
            CategorizeCardsByRarity();
            
            // 订阅事件
            GameEventSystem.Subscribe<PlayerEventArgs>(OnPlayerEvent);
            
            Debug.Log($"✅ 卡牌系统初始化完成 - 卡牌配置数: {cardConfigs.Count}");
        }
        
        /// <summary>
        /// 加载卡牌数据库
        /// </summary>
        private void LoadCardDatabase()
        {
            if (cardDatabase == null)
            {
                cardDatabase = Resources.Load<CardDatabase>("CardDatabase");
                if (cardDatabase == null)
                {
                    Debug.LogWarning("未找到卡牌数据库，创建默认卡牌");
                    CreateDefaultCards();
                }
                else
                {
                    cardConfigs = cardDatabase.cardConfigs;
                }
            }
        }
        
        /// <summary>
        /// 创建默认卡牌
        /// </summary>
        private void CreateDefaultCards()
        {
            // 创建一些基础卡牌配置
            var basicCards = new[]
            {
                new CardConfig { id = 2000001, name = "年轻的苏丹", cardType = CardType.Character, rarity = CardRarity.Legendary,
                    physique = 15, charm = 20, intelligence = 18, fortune = 25, spirit = 22, description = "年轻有为的统治者" },
                
                new CardConfig { id = 2000002, name = "宫廷侍卫", cardType = CardType.Character, rarity = CardRarity.Common,
                    physique = 12, charm = 5, intelligence = 6, fortune = 3, spirit = 8, description = "忠诚的宫廷守卫" },
                
                new CardConfig { id = 2000003, name = "智慧学者", cardType = CardType.Character, rarity = CardRarity.Uncommon,
                    physique = 4, charm = 8, intelligence = 15, fortune = 6, spirit = 12, description = "博学的宫廷学者" },
                
                new CardConfig { id = 2000004, name = "商人", cardType = CardType.Character, rarity = CardRarity.Common,
                    physique = 6, charm = 10, intelligence = 8, fortune = 14, spirit = 7, description = "精明的商人" },
                
                new CardConfig { id = 2000005, name = "神秘法师", cardType = CardType.Character, rarity = CardRarity.Rare,
                    physique = 5, charm = 7, intelligence = 12, fortune = 8, spirit = 18, description = "掌握神秘力量的法师" },
                
                new CardConfig { id = 3000001, name = "黄金宝剑", cardType = CardType.Equipment, rarity = CardRarity.Rare,
                    physique = 8, charm = 3, intelligence = 0, fortune = 5, spirit = 2, description = "闪闪发光的黄金宝剑" },
                
                new CardConfig { id = 3000002, name = "智慧之书", cardType = CardType.Equipment, rarity = CardRarity.Uncommon,
                    physique = 0, charm = 2, intelligence = 10, fortune = 1, spirit = 5, description = "蕴含古老智慧的书籍" },
                
                new CardConfig { id = 4000001, name = "宫廷盛宴", cardType = CardType.Event, rarity = CardRarity.Common,
                    physique = 2, charm = 8, intelligence = 3, fortune = 4, spirit = 3, description = "豪华的宫廷宴会" },
                
                new CardConfig { id = 5000001, name = "外交谈判", cardType = CardType.Skill, rarity = CardRarity.Uncommon,
                    physique = 1, charm = 12, intelligence = 8, fortune = 6, spirit = 3, description = "高超的外交技巧" }
            };
            
            cardConfigs.AddRange(basicCards);
        }
        
        /// <summary>
        /// 按稀有度分类卡牌
        /// </summary>
        private void CategorizeCardsByRarity()
        {
            cardsByRarity.Clear();
            
            foreach (CardRarity rarity in Enum.GetValues(typeof(CardRarity)))
            {
                cardsByRarity[rarity] = new List<CardConfig>();
            }
            
            foreach (var config in cardConfigs)
            {
                cardsByRarity[config.rarity].Add(config);
            }
        }
        
        /// <summary>
        /// 创建卡牌实例
        /// </summary>
        public Card CreateCardInstance(int cardId)
        {
            var config = cardConfigs.FirstOrDefault(c => c.id == cardId);
            if (config == null)
            {
                Debug.LogWarning($"未找到卡牌配置 ID: {cardId}");
                return null;
            }
            
            return CreateCardFromConfig(config);
        }
        
        /// <summary>
        /// 从配置创建卡牌
        /// </summary>
        private Card CreateCardFromConfig(CardConfig config)
        {
            var card = new Card
            {
                id = config.id,
                name = config.name,
                title = config.title,
                description = config.description,
                flavorText = config.flavorText,
                cardType = config.cardType,
                rarity = config.rarity,
                cost = config.cost,
                physique = config.physique,
                charm = config.charm,
                intelligence = config.intelligence,
                fortune = config.fortune,
                spirit = config.spirit,
                iconPath = config.iconPath,
                artworkPath = config.artworkPath,
                tags = new List<string>(config.tags),
                effects = new List<CardEffect>(config.effects)
            };
            
            // 缓存卡牌
            cardCache[card.id] = card;
            
            OnCardCreated?.Invoke(card);
            GameEventSystem.Publish(new CardEventArgs(card, CardEventType.Drawn));
            
            return card;
        }
        
        /// <summary>
        /// 创建随机卡牌
        /// </summary>
        public Card CreateRandomCard(CardRarity? forceRarity = null)
        {
            CardRarity rarity = forceRarity ?? GetRandomRarity();
            var availableCards = cardsByRarity[rarity];
            
            if (availableCards.Count == 0)
            {
                Debug.LogWarning($"没有可用的 {rarity} 稀有度卡牌");
                return null;
            }
            
            var randomConfig = availableCards[UnityEngine.Random.Range(0, availableCards.Count)];
            return CreateCardFromConfig(randomConfig);
        }
        
        /// <summary>
        /// 获取随机稀有度
        /// </summary>
        private CardRarity GetRandomRarity()
        {
            float totalWeight = commonWeight + uncommonWeight + rareWeight + epicWeight + legendaryWeight;
            float randomValue = UnityEngine.Random.Range(0f, totalWeight);
            
            if (randomValue < commonWeight)
                return CardRarity.Common;
            else if (randomValue < commonWeight + uncommonWeight)
                return CardRarity.Uncommon;
            else if (randomValue < commonWeight + uncommonWeight + rareWeight)
                return CardRarity.Rare;
            else if (randomValue < commonWeight + uncommonWeight + rareWeight + epicWeight)
                return CardRarity.Epic;
            else
                return CardRarity.Legendary;
        }
        
        /// <summary>
        /// 创建初始卡组
        /// </summary>
        public List<Card> CreateInitialDeck()
        {
            var deck = new List<Card>();
            
            // 添加基础卡牌
            for (int i = 0; i < initialDeckSize; i++)
            {
                CardRarity rarity;
                
                // 前几张保证是常见卡牌
                if (i < 10)
                    rarity = CardRarity.Common;
                else if (i < 15)
                    rarity = CardRarity.Uncommon;
                else
                    rarity = GetRandomRarity();
                
                var card = CreateRandomCard(rarity);
                if (card != null)
                {
                    deck.Add(card);
                }
            }
            
            Debug.Log($"🃏 创建初始卡组 - 卡牌数: {deck.Count}");
            return deck;
        }
        
        /// <summary>
        /// 使用卡牌
        /// </summary>
        public bool UseCard(Card card, Player player)
        {
            if (card == null || player == null) return false;
            
            if (!card.CanUse())
            {
                Debug.LogWarning($"卡牌 {card.name} 无法使用");
                return false;
            }
            
            // 应用卡牌效果
            card.ApplyEffects(player);
            
            // 标记为已使用
            card.isUsed = true;
            
            OnCardUsed?.Invoke(card, player);
            GameEventSystem.Publish(new CardEventArgs(card, CardEventType.Used));
            
            Debug.Log($"🃏 使用卡牌: {card.name}");
            return true;
        }
        
        /// <summary>
        /// 升级卡牌
        /// </summary>
        public bool UpgradeCard(Card card, int experience)
        {
            if (card == null) return false;
            
            bool leveledUp = card.AddExperience(experience);
            
            if (leveledUp)
            {
                GameEventSystem.Publish(new CardEventArgs(card, CardEventType.LevelUp));
                Debug.Log($"🆙 卡牌升级: {card.name} 达到 {card.level} 级");
            }
            
            return leveledUp;
        }
        
        /// <summary>
        /// 销毁卡牌
        /// </summary>
        public void DestroyCard(Card card)
        {
            if (card == null) return;
            
            cardCache.Remove(card.id);
            
            OnCardDestroyed?.Invoke(card);
            GameEventSystem.Publish(new CardEventArgs(card, CardEventType.Destroyed));
            
            Debug.Log($"💥 销毁卡牌: {card.name}");
        }
        
        /// <summary>
        /// 获取卡牌配置
        /// </summary>
        public CardConfig GetCardConfig(int cardId)
        {
            return cardConfigs.FirstOrDefault(c => c.id == cardId);
        }
        
        /// <summary>
        /// 根据类型获取卡牌配置
        /// </summary>
        public List<CardConfig> GetCardConfigsByType(CardType cardType)
        {
            return cardConfigs.Where(c => c.cardType == cardType).ToList();
        }
        
        /// <summary>
        /// 根据稀有度获取卡牌配置
        /// </summary>
        public List<CardConfig> GetCardConfigsByRarity(CardRarity rarity)
        {
            return cardsByRarity.ContainsKey(rarity) ? cardsByRarity[rarity] : new List<CardConfig>();
        }
        
        /// <summary>
        /// 处理玩家事件
        /// </summary>
        private void OnPlayerEvent(PlayerEventArgs args)
        {
            // 根据玩家事件调整卡牌系统行为
            if (args.EventType == PlayerEventType.LevelUp)
            {
                // 玩家升级时可能解锁新卡牌
                Debug.Log("🎉 玩家升级，检查新卡牌解锁");
            }
        }
        
        private void OnDestroy()
        {
            GameEventSystem.Unsubscribe<PlayerEventArgs>(OnPlayerEvent);
        }
    }
    
    /// <summary>
    /// 卡牌配置
    /// </summary>
    [Serializable]
    public class CardConfig
    {
        public int id;
        public string name;
        public string title;
        public string description;
        public string flavorText;
        public CardType cardType;
        public CardRarity rarity;
        public int cost;
        public int physique;
        public int charm;
        public int intelligence;
        public int fortune;
        public int spirit;
        public string iconPath;
        public string artworkPath;
        public List<string> tags = new List<string>();
        public List<CardEffect> effects = new List<CardEffect>();
    }
    
    /// <summary>
    /// 卡牌数据库
    /// </summary>
    [CreateAssetMenu(fileName = "CardDatabase", menuName = "Sultan's Game/Card Database")]
    public class CardDatabase : ScriptableObject
    {
        public List<CardConfig> cardConfigs = new List<CardConfig>();
    }
}
