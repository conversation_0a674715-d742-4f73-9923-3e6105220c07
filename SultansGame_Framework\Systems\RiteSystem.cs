using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using SultansGameClone.Core;
using SultansGameClone.Events;

namespace SultansGameClone.Systems
{
    /// <summary>
    /// 仪式系统 - 苏丹的游戏核心仪式机制
    /// 管理仪式的执行、条件检查和奖励发放
    /// </summary>
    public class RiteSystem : MonoBehaviour
    {
        [Header("仪式配置")]
        public List<RiteData> availableRites = new List<RiteData>();
        public int maxActiveRites = 3;
        
        [Header("当前状态")]
        public List<RiteData> activeRites = new List<RiteData>();
        public List<RiteData> completedRites = new List<RiteData>();
        
        // 单例
        public static RiteSystem Instance { get; private set; }
        
        // 事件
        public event Action<RiteData> OnRiteStarted;
        public event Action<RiteData, bool> OnRiteCompleted;
        public event Action<RiteData> OnRiteUnlocked;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeRiteSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 初始化仪式系统
        /// </summary>
        private void InitializeRiteSystem()
        {
            Debug.Log("🔮 初始化仪式系统...");
            
            // 加载仪式数据
            LoadRiteData();
            
            // 订阅游戏事件
            GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Subscribe<PlayerEventArgs>(OnPlayerEvent);
            
            Debug.Log($"✅ 仪式系统初始化完成 - 可用仪式: {availableRites.Count}");
        }
        
        /// <summary>
        /// 加载仪式数据
        /// </summary>
        private void LoadRiteData()
        {
            // 创建一些示例仪式
            CreateSampleRites();
        }
        
        /// <summary>
        /// 创建示例仪式
        /// </summary>
        private void CreateSampleRites()
        {
            // 力量仪式
            var strengthRite = new RiteData
            {
                id = 3300001,
                name = "力量仪式",
                description = "通过特定的卡牌组合来增强体魄属性",
                riteType = RiteType.AttributeBoost,
                difficulty = RiteDifficulty.Easy,
                requirements = new List<RiteRequirement>
                {
                    new RiteRequirement { requirementType = RequirementType.CardWithAttribute, attributeType = AttributeType.Physique, minValue = 10 },
                    new RiteRequirement { requirementType = RequirementType.PlayerLevel, minValue = 1 }
                },
                rewards = new List<RiteReward>
                {
                    new RiteReward { rewardType = RewardType.AttributeBonus, attributeType = AttributeType.Physique, value = 5 },
                    new RiteReward { rewardType = RewardType.Experience, value = 50 }
                }
            };
            
            // 智慧仪式
            var wisdomRite = new RiteData
            {
                id = 3300002,
                name = "智慧仪式",
                description = "通过学习和思考来提升智慧属性",
                riteType = RiteType.AttributeBoost,
                difficulty = RiteDifficulty.Normal,
                requirements = new List<RiteRequirement>
                {
                    new RiteRequirement { requirementType = RequirementType.CardWithAttribute, attributeType = AttributeType.Intelligence, minValue = 15 },
                    new RiteRequirement { requirementType = RequirementType.CardCount, minValue = 3 }
                },
                rewards = new List<RiteReward>
                {
                    new RiteReward { rewardType = RewardType.AttributeBonus, attributeType = AttributeType.Intelligence, value = 8 },
                    new RiteReward { rewardType = RewardType.DrawCard, value = 2 }
                }
            };
            
            // 财富仪式
            var fortuneRite = new RiteData
            {
                id = 3300003,
                name = "财富仪式",
                description = "通过商业活动来积累财富",
                riteType = RiteType.ResourceGain,
                difficulty = RiteDifficulty.Hard,
                requirements = new List<RiteRequirement>
                {
                    new RiteRequirement { requirementType = RequirementType.CardWithAttribute, attributeType = AttributeType.Fortune, minValue = 20 },
                    new RiteRequirement { requirementType = RequirementType.CardWithTag, stringValue = "商业" }
                },
                rewards = new List<RiteReward>
                {
                    new RiteReward { rewardType = RewardType.AttributeBonus, attributeType = AttributeType.Fortune, value = 12 },
                    new RiteReward { rewardType = RewardType.UnlockCard, value = 2001 }
                }
            };
            
            availableRites.AddRange(new[] { strengthRite, wisdomRite, fortuneRite });
        }
        
        /// <summary>
        /// 开始仪式
        /// </summary>
        public bool StartRite(int riteId)
        {
            var rite = availableRites.FirstOrDefault(r => r.id == riteId);
            if (rite == null)
            {
                Debug.LogWarning($"未找到仪式 ID: {riteId}");
                return false;
            }
            
            if (activeRites.Count >= maxActiveRites)
            {
                Debug.LogWarning("活跃仪式数量已达上限");
                return false;
            }
            
            if (activeRites.Any(r => r.id == riteId))
            {
                Debug.LogWarning($"仪式 {rite.name} 已经在进行中");
                return false;
            }
            
            // 检查前置条件
            if (!CheckRitePrerequisites(rite))
            {
                Debug.LogWarning($"不满足仪式 {rite.name} 的前置条件");
                return false;
            }
            
            activeRites.Add(rite);
            rite.isActive = true;
            rite.startTime = DateTime.Now;
            
            OnRiteStarted?.Invoke(rite);
            GameEventSystem.Publish(new RiteEventArgs(riteId, RiteEventType.Started));
            
            Debug.Log($"🔮 开始仪式: {rite.name}");
            return true;
        }
        
        /// <summary>
        /// 检查仪式前置条件
        /// </summary>
        private bool CheckRitePrerequisites(RiteData rite)
        {
            var player = GameManager.Instance?.currentPlayer;
            if (player == null) return false;
            
            // 检查玩家等级
            if (rite.minPlayerLevel > 0 && player.level < rite.minPlayerLevel)
            {
                return false;
            }
            
            // 检查已完成的仪式
            foreach (var prereqId in rite.prerequisiteRites)
            {
                if (!completedRites.Any(r => r.id == prereqId))
                {
                    return false;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// 检查仪式完成条件
        /// </summary>
        public void CheckRiteCompletion()
        {
            var player = GameManager.Instance?.currentPlayer;
            if (player == null) return;
            
            var ritesToComplete = new List<RiteData>();
            
            foreach (var rite in activeRites)
            {
                if (CheckRiteRequirements(rite, player))
                {
                    ritesToComplete.Add(rite);
                }
            }
            
            foreach (var rite in ritesToComplete)
            {
                CompleteRite(rite);
            }
        }
        
        /// <summary>
        /// 检查仪式需求
        /// </summary>
        private bool CheckRiteRequirements(RiteData rite, Player player)
        {
            foreach (var requirement in rite.requirements)
            {
                if (!CheckSingleRequirement(requirement, player))
                {
                    return false;
                }
            }
            return true;
        }
        
        /// <summary>
        /// 检查单个需求
        /// </summary>
        private bool CheckSingleRequirement(RiteRequirement requirement, Player player)
        {
            switch (requirement.requirementType)
            {
                case RequirementType.PlayerLevel:
                    return player.level >= requirement.minValue;
                
                case RequirementType.CardCount:
                    return player.hand.Count >= requirement.minValue;
                
                case RequirementType.CardWithAttribute:
                    var cardsWithAttribute = player.hand.GetCards()
                        .Where(c => c.GetAttribute(requirement.attributeType) >= requirement.minValue);
                    return cardsWithAttribute.Any();
                
                case RequirementType.CardWithTag:
                    return player.hand.GetCards().Any(c => c.HasTag(requirement.stringValue));
                
                case RequirementType.TotalAttribute:
                    return player.GetTotalAttribute(requirement.attributeType) >= requirement.minValue;
                
                case RequirementType.EquippedCards:
                    return player.table.GetCards().Count(c => c.isEquipped) >= requirement.minValue;
                
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 完成仪式
        /// </summary>
        private void CompleteRite(RiteData rite)
        {
            activeRites.Remove(rite);
            completedRites.Add(rite);
            
            rite.isActive = false;
            rite.isCompleted = true;
            rite.completionTime = DateTime.Now;
            
            // 给予奖励
            GiveRiteRewards(rite);
            
            OnRiteCompleted?.Invoke(rite, true);
            GameEventSystem.Publish(new RiteEventArgs(rite.id, RiteEventType.Completed, true, null, 
                new Dictionary<string, object> { { "rewards", rite.rewards } }));
            
            Debug.Log($"✅ 完成仪式: {rite.name}");
        }
        
        /// <summary>
        /// 给予仪式奖励
        /// </summary>
        private void GiveRiteRewards(RiteData rite)
        {
            var player = GameManager.Instance?.currentPlayer;
            if (player == null) return;
            
            foreach (var reward in rite.rewards)
            {
                ApplyReward(reward, player);
            }
        }
        
        /// <summary>
        /// 应用奖励
        /// </summary>
        private void ApplyReward(RiteReward reward, Player player)
        {
            switch (reward.rewardType)
            {
                case RewardType.Experience:
                    player.AddExperience(reward.value);
                    break;
                
                case RewardType.AttributeBonus:
                    // 临时属性加成（需要在Player类中实现）
                    Debug.Log($"获得 {reward.attributeType} +{reward.value}");
                    break;
                
                case RewardType.DrawCard:
                    for (int i = 0; i < reward.value; i++)
                    {
                        player.DrawCard();
                    }
                    break;
                
                case RewardType.UnlockCard:
                    player.unlockedCards.Add(reward.value);
                    break;
                
                case RewardType.Healing:
                    player.Heal(reward.value);
                    break;
            }
        }
        
        /// <summary>
        /// 取消仪式
        /// </summary>
        public bool CancelRite(int riteId)
        {
            var rite = activeRites.FirstOrDefault(r => r.id == riteId);
            if (rite == null) return false;
            
            activeRites.Remove(rite);
            rite.isActive = false;
            
            GameEventSystem.Publish(new RiteEventArgs(riteId, RiteEventType.Cancelled));
            
            Debug.Log($"❌ 取消仪式: {rite.name}");
            return true;
        }
        
        /// <summary>
        /// 获取可用仪式
        /// </summary>
        public List<RiteData> GetAvailableRites()
        {
            return availableRites.Where(r => !r.isActive && !r.isCompleted).ToList();
        }
        
        /// <summary>
        /// 处理卡牌事件
        /// </summary>
        private void OnCardEvent(CardEventArgs args)
        {
            // 当卡牌状态改变时检查仪式完成条件
            if (args.EventType == CardEventType.Equipped || 
                args.EventType == CardEventType.Unequipped ||
                args.EventType == CardEventType.Drawn)
            {
                CheckRiteCompletion();
            }
        }
        
        /// <summary>
        /// 处理玩家事件
        /// </summary>
        private void OnPlayerEvent(PlayerEventArgs args)
        {
            // 当玩家状态改变时检查仪式完成条件
            if (args.EventType == PlayerEventType.LevelUp ||
                args.EventType == PlayerEventType.AttributeChanged)
            {
                CheckRiteCompletion();
            }
        }
        
        private void OnDestroy()
        {
            GameEventSystem.Unsubscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Unsubscribe<PlayerEventArgs>(OnPlayerEvent);
        }
    }
}
