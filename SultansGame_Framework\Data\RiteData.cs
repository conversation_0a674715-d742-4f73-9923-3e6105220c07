using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using SultansGameClone.Core;

namespace SultansGameClone.Systems
{
    /// <summary>
    /// 仪式数据模型 - 定义仪式的所有属性和行为
    /// </summary>
    [Serializable]
    [CreateAssetMenu(fileName = "New Rite", menuName = "Sultan's Game/Rite Data")]
    public class RiteData : ScriptableObject
    {
        [Header("基础信息")]
        public int id;                          // 仪式ID
        public string riteName;                 // 仪式名称
        public string description;              // 仪式描述
        public string flavorText;               // 风味文本
        
        [Header("仪式类型")]
        public RiteType riteType;               // 仪式类型
        public RiteDifficulty difficulty;       // 难度等级
        public RiteCategory category;           // 仪式分类
        
        [Header("条件要求")]
        public List<RiteRequirement> requirements = new List<RiteRequirement>(); // 完成条件
        public List<int> prerequisiteRites = new List<int>();                    // 前置仪式
        public int minPlayerLevel = 1;                                           // 最低玩家等级
        public int maxPlayerLevel = 999;                                         // 最高玩家等级
        
        [Header("奖励设置")]
        public List<RiteReward> rewards = new List<RiteReward>();               // 奖励列表
        public bool isRepeatable = false;                                       // 是否可重复
        public int cooldownHours = 0;                                           // 冷却时间（小时）
        
        [Header("视觉资源")]
        public Sprite riteIcon;                 // 仪式图标
        public Sprite riteBackground;           // 背景图片
        public Color riteColor = Color.white;   // 主题颜色
        
        [Header("音效设置")]
        public AudioClip startSound;            // 开始音效
        public AudioClip completeSound;         // 完成音效
        public AudioClip failSound;             // 失败音效
        
        [Header("运行时状态")]
        [SerializeField] private bool _isActive = false;        // 是否激活
        [SerializeField] private bool _isCompleted = false;     // 是否完成
        [SerializeField] private bool _isUnlocked = true;       // 是否解锁
        [SerializeField] private DateTime _startTime;           // 开始时间
        [SerializeField] private DateTime _completionTime;      // 完成时间
        [SerializeField] private DateTime _lastCompletedTime;   // 上次完成时间
        [SerializeField] private int _completionCount = 0;      // 完成次数
        
        // 属性访问器
        public bool isActive 
        { 
            get => _isActive; 
            set => _isActive = value; 
        }
        
        public bool isCompleted 
        { 
            get => _isCompleted; 
            set => _isCompleted = value; 
        }
        
        public bool isUnlocked 
        { 
            get => _isUnlocked; 
            set => _isUnlocked = value; 
        }
        
        public DateTime startTime 
        { 
            get => _startTime; 
            set => _startTime = value; 
        }
        
        public DateTime completionTime 
        { 
            get => _completionTime; 
            set => _completionTime = value; 
        }
        
        public DateTime lastCompletedTime 
        { 
            get => _lastCompletedTime; 
            set => _lastCompletedTime = value; 
        }
        
        public int completionCount 
        { 
            get => _completionCount; 
            set => _completionCount = value; 
        }
        
        /// <summary>
        /// 检查是否可以开始仪式
        /// </summary>
        public bool CanStart(Player player)
        {
            // 检查是否已解锁
            if (!isUnlocked) return false;
            
            // 检查是否已激活
            if (isActive) return false;
            
            // 检查玩家等级
            if (player.level < minPlayerLevel || player.level > maxPlayerLevel) return false;
            
            // 检查冷却时间
            if (!isRepeatable && isCompleted) return false;
            
            if (cooldownHours > 0 && lastCompletedTime != default)
            {
                var cooldownEnd = lastCompletedTime.AddHours(cooldownHours);
                if (DateTime.Now < cooldownEnd) return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 获取冷却剩余时间
        /// </summary>
        public TimeSpan GetRemainingCooldown()
        {
            if (cooldownHours <= 0 || lastCompletedTime == default)
                return TimeSpan.Zero;
            
            var cooldownEnd = lastCompletedTime.AddHours(cooldownHours);
            var remaining = cooldownEnd - DateTime.Now;
            
            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }
        
        /// <summary>
        /// 获取进度百分比
        /// </summary>
        public float GetProgress(Player player)
        {
            if (requirements.Count == 0) return 1f;
            
            int metRequirements = 0;
            foreach (var requirement in requirements)
            {
                if (IsRequirementMet(requirement, player))
                {
                    metRequirements++;
                }
            }
            
            return (float)metRequirements / requirements.Count;
        }
        
        /// <summary>
        /// 检查单个需求是否满足
        /// </summary>
        private bool IsRequirementMet(RiteRequirement requirement, Player player)
        {
            switch (requirement.requirementType)
            {
                case RequirementType.PlayerLevel:
                    return player.level >= requirement.minValue;
                
                case RequirementType.CardCount:
                    return player.hand.Count >= requirement.minValue;
                
                case RequirementType.CardWithAttribute:
                    return player.hand.GetCards().Any(c => c.GetAttribute(requirement.attributeType) >= requirement.minValue);
                
                case RequirementType.CardWithTag:
                    return player.hand.GetCards().Any(c => c.HasTag(requirement.stringValue));
                
                case RequirementType.TotalAttribute:
                    return player.GetTotalAttribute(requirement.attributeType) >= requirement.minValue;
                
                case RequirementType.EquippedCards:
                    return player.table.GetCards().Count(c => c.isEquipped) >= requirement.minValue;
                
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 重置仪式状态
        /// </summary>
        public void Reset()
        {
            isActive = false;
            isCompleted = false;
            startTime = default;
            completionTime = default;
        }
        
        /// <summary>
        /// 克隆仪式数据
        /// </summary>
        public RiteData Clone()
        {
            var clone = CreateInstance<RiteData>();
            clone.id = this.id;
            clone.riteName = this.riteName;
            clone.description = this.description;
            clone.flavorText = this.flavorText;
            clone.riteType = this.riteType;
            clone.difficulty = this.difficulty;
            clone.category = this.category;
            clone.requirements = new List<RiteRequirement>(this.requirements);
            clone.prerequisiteRites = new List<int>(this.prerequisiteRites);
            clone.minPlayerLevel = this.minPlayerLevel;
            clone.maxPlayerLevel = this.maxPlayerLevel;
            clone.rewards = new List<RiteReward>(this.rewards);
            clone.isRepeatable = this.isRepeatable;
            clone.cooldownHours = this.cooldownHours;
            clone.riteIcon = this.riteIcon;
            clone.riteBackground = this.riteBackground;
            clone.riteColor = this.riteColor;
            clone.startSound = this.startSound;
            clone.completeSound = this.completeSound;
            clone.failSound = this.failSound;
            
            return clone;
        }
    }
    
    /// <summary>
    /// 仪式需求
    /// </summary>
    [Serializable]
    public class RiteRequirement
    {
        public RequirementType requirementType;     // 需求类型
        public AttributeType attributeType;         // 属性类型
        public int minValue;                        // 最小值
        public int maxValue;                        // 最大值
        public string stringValue;                  // 字符串值
        public string description;                  // 需求描述
    }
    
    /// <summary>
    /// 仪式奖励
    /// </summary>
    [Serializable]
    public class RiteReward
    {
        public RewardType rewardType;               // 奖励类型
        public AttributeType attributeType;         // 属性类型
        public int value;                           // 奖励数值
        public string stringValue;                  // 字符串值
        public string description;                  // 奖励描述
    }
    
    // ==================== 枚举定义 ====================
    
    /// <summary>
    /// 仪式类型
    /// </summary>
    public enum RiteType
    {
        AttributeBoost,     // 属性提升
        ResourceGain,       // 资源获取
        CardUnlock,         // 卡牌解锁
        StoryProgress,      // 剧情推进
        Achievement,        // 成就解锁
        Special             // 特殊仪式
    }
    
    /// <summary>
    /// 仪式难度
    /// </summary>
    public enum RiteDifficulty
    {
        Easy,               // 简单
        Normal,             // 普通
        Hard,               // 困难
        Expert,             // 专家
        Legendary           // 传说
    }
    
    /// <summary>
    /// 仪式分类
    /// </summary>
    public enum RiteCategory
    {
        Combat,             // 战斗
        Social,             // 社交
        Knowledge,          // 知识
        Commerce,           // 商业
        Spiritual,          // 精神
        Adventure           // 冒险
    }
    
    /// <summary>
    /// 需求类型
    /// </summary>
    public enum RequirementType
    {
        PlayerLevel,        // 玩家等级
        CardCount,          // 卡牌数量
        CardWithAttribute,  // 具有特定属性的卡牌
        CardWithTag,        // 具有特定标签的卡牌
        TotalAttribute,     // 总属性值
        EquippedCards,      // 装备的卡牌
        CompletedQuests,    // 完成的任务
        UnlockedCards       // 解锁的卡牌
    }
    
    /// <summary>
    /// 奖励类型
    /// </summary>
    public enum RewardType
    {
        Experience,         // 经验值
        AttributeBonus,     // 属性加成
        DrawCard,           // 抽卡
        UnlockCard,         // 解锁卡牌
        Healing,            // 治疗
        UnlockRite,         // 解锁仪式
        Achievement,        // 成就
        Story               // 剧情
    }
}
