using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using DG.Tweening;

namespace SultansGameClone
{
    // ==================== 卡牌UI控制器 ====================
    
    public class SultansCardController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerClickHandler, IPointerEnterHandler, 
                                  IPointerExitHandler, IBeginDragHandler, IDragHandler, IEndDragHandler
    {
        [Header("UI组件")]
        public Image cardImage;
        public Image cardFrame;
        public Text nameText;
        public Text titleText;
        public Text descriptionText;
        public Text[] attributeTexts = new Text[5]; // 五维属性文本
        public Image[] attributeIcons = new Image[5]; // 五维属性图标
        public GameObject selectionGlow;
        public GameObject lockIcon;
        
        [Header("动画设置")]
        public float hoverScale = 1.1f;
        public float selectScale = 1.05f;
        public float animationDuration = 0.3f;
        
        [Header("拖拽设置")]
        public float dragAlpha = 0.8f;
        public Vector3 dragScale = Vector3.one * 1.2f;
        
        // 私有字段
        private Card cardData;
        private Vector3 originalPosition;
        private Vector3 originalScale;
        private CanvasGroup canvasGroup;
        private bool isDragging = false;
        private bool isHovering = false;
        
        // 属性名称数组
        private readonly string[] attributeNames = { "physique", "charm", "intelligence", "fortune", "spirit" };
        
        private void Awake()
        {
            // 获取或添加CanvasGroup组件
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }
            
            // 保存原始变换
            originalPosition = transform.position;
            originalScale = transform.localScale;
            
            // 初始化UI状态
            if (selectionGlow != null)
                selectionGlow.SetActive(false);
            
            if (lockIcon != null)
                lockIcon.SetActive(false);
        }
        
        // 设置卡牌数据
        public void SetCard(Card card)
        {
            cardData = card;
            UpdateVisuals();
        }
        
        // 更新视觉效果
        public void UpdateVisuals()
        {
            if (cardData == null) return;
            
            // 更新基础信息
            if (nameText != null)
                nameText.text = cardData.name;
            
            if (titleText != null)
                titleText.text = cardData.title;
            
            if (descriptionText != null)
                descriptionText.text = cardData.description;
            
            // 更新属性显示
            UpdateAttributeDisplay();
            
            // 更新卡牌框架颜色（根据稀有度）
            UpdateFrameColor();
            
            // 更新状态显示
            UpdateStateDisplay();
            
            // 加载卡牌图片
            LoadCardImage();
        }
        
        // 更新属性显示
        private void UpdateAttributeDisplay()
        {
            int[] attributeValues = {
                cardData.physique,
                cardData.charm,
                cardData.intelligence,
                cardData.fortune,
                cardData.spirit
            };
            
            for (int i = 0; i < attributeTexts.Length && i < attributeValues.Length; i++)
            {
                if (attributeTexts[i] != null)
                {
                    attributeTexts[i].text = attributeValues[i].ToString();
                    
                    // 根据属性值设置颜色
                    Color textColor = GetAttributeColor(attributeValues[i]);
                    attributeTexts[i].color = textColor;
                }
            }
        }
        
        // 根据属性值获取颜色
        private Color GetAttributeColor(int value)
        {
            if (value >= 80) return Color.red;        // 高属性 - 红色
            if (value >= 60) return Color.yellow;     // 中高属性 - 黄色
            if (value >= 40) return Color.green;      // 中等属性 - 绿色
            if (value >= 20) return Color.cyan;       // 中低属性 - 青色
            return Color.white;                       // 低属性 - 白色
        }
        
        // 更新框架颜色
        private void UpdateFrameColor()
        {
            if (cardFrame == null) return;
            
            Color frameColor = GetRarityColor(cardData.rarity);
            cardFrame.color = frameColor;
        }
        
        // 根据稀有度获取颜色
        private Color GetRarityColor(Rarity rarity)
        {
            switch (rarity)
            {
                case Rarity.Common: return Color.gray;
                case Rarity.Rare: return Color.blue;
                case Rarity.Epic: return Color.magenta;
                case Rarity.Legendary: return Color.yellow;
                case Rarity.Mythic: return Color.red;
                default: return Color.white;
            }
        }
        
        // 更新状态显示
        private void UpdateStateDisplay()
        {
            // 选中状态
            if (selectionGlow != null)
                selectionGlow.SetActive(cardData.isSelected);
            
            // 锁定状态
            if (lockIcon != null)
                lockIcon.SetActive(cardData.isLocked);
            
            // 设置交互性
            canvasGroup.interactable = !cardData.isLocked;
        }
        
        // 加载卡牌图片
        private void LoadCardImage()
        {
            if (cardImage == null || string.IsNullOrEmpty(cardData.iconPath)) return;
            
            // 从Resources加载图片
            Sprite cardSprite = Resources.Load<Sprite>(cardData.iconPath);
            if (cardSprite != null)
            {
                cardImage.sprite = cardSprite;
            }
            else
            {
                Debug.LogWarning($"⚠️ 无法加载卡牌图片: {cardData.iconPath}");
            }
        }
        
        // ==================== 事件处理 ====================
        
        // 点击事件
        public void OnPointerClick(PointerEventData eventData)
        {
            if (cardData == null || cardData.isLocked) return;
            
            if (eventData.button == PointerEventData.InputButton.Left)
            {
                // 左键点击 - 选择卡牌
                GameManager.Instance.SelectCard(cardData);
            }
            else if (eventData.button == PointerEventData.InputButton.Right)
            {
                // 右键点击 - 快速装备/卸下
                QuickEquipToggle();
            }
        }
        
        // 鼠标进入
        public void OnPointerEnter(PointerEventData eventData)
        {
            if (isDragging) return;
            
            isHovering = true;
            
            // 悬停放大效果
            transform.DOScale(originalScale * hoverScale, animationDuration)
                .SetEase(Ease.OutBack);
            
            // 显示详细信息（可选）
            ShowTooltip();
        }
        
        // 鼠标离开
        public void OnPointerExit(PointerEventData eventData)
        {
            if (isDragging) return;
            
            isHovering = false;
            
            // 恢复原始大小
            if (!cardData.isSelected)
            {
                transform.DOScale(originalScale, animationDuration)
                    .SetEase(Ease.OutQuart);
            }
            
            // 隐藏详细信息
            HideTooltip();
        }
        
        // 开始拖拽
        public void OnBeginDrag(PointerEventData eventData)
        {
            if (cardData == null || cardData.isLocked) return;
            
            isDragging = true;
            originalPosition = transform.position;
            
            // 拖拽视觉效果
            canvasGroup.alpha = dragAlpha;
            transform.localScale = dragScale;
            
            // 移到最前层
            transform.SetAsLastSibling();
            
            Debug.Log($"🖱️ 开始拖拽卡牌: {cardData.name}");
        }
        
        // 拖拽中
        public void OnDrag(PointerEventData eventData)
        {
            if (!isDragging) return;
            
            // 跟随鼠标位置
            transform.position = eventData.position;
        }
        
        // 结束拖拽
        public void OnEndDrag(PointerEventData eventData)
        {
            if (!isDragging) return;
            
            isDragging = false;
            
            // 恢复视觉效果
            canvasGroup.alpha = 1f;
            transform.localScale = originalScale;
            
            // 检查拖拽目标
            GameObject dropTarget = eventData.pointerCurrentRaycast.gameObject;
            bool validDrop = HandleDrop(dropTarget);
            
            if (!validDrop)
            {
                // 无效拖拽，返回原位置
                transform.DOMove(originalPosition, animationDuration)
                    .SetEase(Ease.OutBack);
                
                // 播放错误动画
                PlayErrorAnimation();
            }
            
            Debug.Log($"🖱️ 结束拖拽卡牌: {cardData.name}");
        }
        
        // 处理拖拽放置
        private bool HandleDrop(GameObject dropTarget)
        {
            if (dropTarget == null) return false;
            
            // 检查是否拖拽到有效区域
            DropZone dropZone = dropTarget.GetComponent<DropZone>();
            if (dropZone == null)
            {
                dropZone = dropTarget.GetComponentInParent<DropZone>();
            }
            
            if (dropZone != null && dropZone.CanAcceptCard(cardData))
            {
                return dropZone.AcceptCard(cardData);
            }
            
            return false;
        }
        
        // 快速装备切换
        private void QuickEquipToggle()
        {
            if (cardData.isEquipped)
            {
                GameManager.Instance.UnequipCard(cardData);
            }
            else
            {
                GameManager.Instance.EquipCard(cardData);
            }
        }
        
        // 显示工具提示
        private void ShowTooltip()
        {
            // TODO: 实现工具提示显示
            Debug.Log($"💡 显示卡牌详情: {cardData.name}");
        }
        
        // 隐藏工具提示
        private void HideTooltip()
        {
            // TODO: 实现工具提示隐藏
        }
        
        // 播放错误动画
        private void PlayErrorAnimation()
        {
            // 摇晃动画
            transform.DOShakePosition(0.5f, 10f, 20, 90f, false, true)
                .SetEase(Ease.OutQuart);
        }
        
        // 播放选择动画
        public void PlaySelectAnimation()
        {
            transform.DOScale(originalScale * selectScale, animationDuration)
                .SetEase(Ease.OutBack);
        }
        
        // 播放取消选择动画
        public void PlayDeselectAnimation()
        {
            if (!isHovering)
            {
                transform.DOScale(originalScale, animationDuration)
                    .SetEase(Ease.OutQuart);
            }
        }
        
        // 获取卡牌数据
        public Card GetCard()
        {
            return cardData;
        }
    }
}
