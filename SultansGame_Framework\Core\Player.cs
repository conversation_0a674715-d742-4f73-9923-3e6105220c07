using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace SultansGameClone.Core
{
    /// <summary>
    /// 玩家数据模型 - 苏丹的游戏玩家系统
    /// 管理玩家状态、属性、卡牌集合等
    /// </summary>
    [Serializable]
    public class Player
    {
        [Header("基础信息")]
        public string playerId;             // 玩家ID
        public string playerName;           // 玩家名称
        public int level = 1;               // 玩家等级
        public int experience = 0;          // 经验值
        public int round = 1;               // 当前回合
        public int day = 1;                 // 当前天数
        
        [Header("生命值系统")]
        public int currentHealth = 100;     // 当前生命值
        public int maxHealth = 100;         // 最大生命值
        public bool isAlive = true;         // 是否存活
        
        [Header("基础属性")]
        public int basePhysique = 10;       // 基础体魄
        public int baseCharm = 10;          // 基础魅力
        public int baseIntelligence = 10;   // 基础智慧
        public int baseFortune = 10;        // 基础财富
        public int baseSpirit = 10;         // 基础精神
        
        [Header("卡牌集合")]
        public CardCollection hand;         // 手牌
        public CardCollection deck;         // 卡组
        public CardCollection table;        // 桌面卡牌
        public CardCollection graveyard;    // 墓地
        public CardCollection inventory;    // 库存
        
        [Header("游戏状态")]
        public bool isGameActive = false;   // 游戏是否进行中
        public DateTime lastPlayTime;       // 最后游戏时间
        public GameDifficulty difficulty;   // 游戏难度
        
        [Header("解锁内容")]
        public List<int> unlockedCards;     // 已解锁卡牌
        public List<int> unlockedRites;     // 已解锁仪式
        public List<int> completedQuests;   // 已完成任务
        
        [Header("游戏数据")]
        public Dictionary<string, object> gameData; // 游戏数据存储
        public List<Achievement> achievements;       // 成就系统
        
        // 事件
        public event Action<int> OnLevelUp;
        public event Action<int> OnHealthChanged;
        public event Action<AttributeType, int> OnAttributeChanged;
        
        // 构造函数
        public Player()
        {
            playerId = Guid.NewGuid().ToString();
            lastPlayTime = DateTime.Now;
            
            // 初始化卡牌集合
            hand = new CardCollection("Hand", 10);
            deck = new CardCollection("Deck");
            table = new CardCollection("Table", 5);
            graveyard = new CardCollection("Graveyard");
            inventory = new CardCollection("Inventory");
            
            // 初始化列表
            unlockedCards = new List<int>();
            unlockedRites = new List<int>();
            completedQuests = new List<int>();
            gameData = new Dictionary<string, object>();
            achievements = new List<Achievement>();
            
            difficulty = GameDifficulty.Normal;
        }
        
        /// <summary>
        /// 初始化玩家
        /// </summary>
        public void Initialize(string name)
        {
            playerName = name;
            isGameActive = true;
            lastPlayTime = DateTime.Now;
            
            // 添加初始卡牌
            AddInitialCards();
            
            Debug.Log($"玩家 {playerName} 初始化完成");
        }
        
        /// <summary>
        /// 获取总属性值（基础+装备加成）
        /// </summary>
        public int GetTotalAttribute(AttributeType attributeType)
        {
            int baseValue = attributeType switch
            {
                AttributeType.Physique => basePhysique,
                AttributeType.Charm => baseCharm,
                AttributeType.Intelligence => baseIntelligence,
                AttributeType.Fortune => baseFortune,
                AttributeType.Spirit => baseSpirit,
                _ => 0
            };
            
            // 计算装备加成
            int equipmentBonus = GetEquipmentBonus(attributeType);
            
            // 计算等级加成
            int levelBonus = (level - 1) * 2;
            
            return baseValue + equipmentBonus + levelBonus;
        }
        
        /// <summary>
        /// 获取装备加成
        /// </summary>
        private int GetEquipmentBonus(AttributeType attributeType)
        {
            int bonus = 0;
            
            foreach (var card in table.GetCards())
            {
                if (card.isEquipped)
                {
                    bonus += card.GetAttribute(attributeType);
                }
            }
            
            return bonus;
        }
        
        /// <summary>
        /// 添加经验值
        /// </summary>
        public void AddExperience(int exp)
        {
            experience += exp;
            int requiredExp = level * 100;
            
            if (experience >= requiredExp)
            {
                LevelUp();
            }
        }
        
        /// <summary>
        /// 升级
        /// </summary>
        private void LevelUp()
        {
            level++;
            experience = 0;
            
            // 升级奖励
            maxHealth += 10;
            currentHealth = maxHealth;
            
            // 属性提升
            basePhysique += 1;
            baseCharm += 1;
            baseIntelligence += 1;
            baseFortune += 1;
            baseSpirit += 1;
            
            OnLevelUp?.Invoke(level);
            Debug.Log($"玩家升级到 {level} 级!");
        }
        
        /// <summary>
        /// 受到伤害
        /// </summary>
        public void TakeDamage(int damage)
        {
            currentHealth = Mathf.Max(0, currentHealth - damage);
            OnHealthChanged?.Invoke(currentHealth);
            
            if (currentHealth <= 0)
            {
                isAlive = false;
                Debug.Log("玩家死亡!");
            }
        }
        
        /// <summary>
        /// 治疗
        /// </summary>
        public void Heal(int amount)
        {
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            OnHealthChanged?.Invoke(currentHealth);
        }
        
        /// <summary>
        /// 添加卡牌到手牌
        /// </summary>
        public bool AddCardToHand(Card card)
        {
            return hand.AddCard(card);
        }
        
        /// <summary>
        /// 从手牌移除卡牌
        /// </summary>
        public bool RemoveCardFromHand(Card card)
        {
            return hand.RemoveCard(card);
        }
        
        /// <summary>
        /// 装备卡牌
        /// </summary>
        public bool EquipCard(Card card)
        {
            if (table.AddCard(card))
            {
                card.isEquipped = true;
                hand.RemoveCard(card);
                
                // 触发属性变化事件
                OnAttributeChanged?.Invoke(AttributeType.Physique, GetTotalAttribute(AttributeType.Physique));
                OnAttributeChanged?.Invoke(AttributeType.Charm, GetTotalAttribute(AttributeType.Charm));
                OnAttributeChanged?.Invoke(AttributeType.Intelligence, GetTotalAttribute(AttributeType.Intelligence));
                OnAttributeChanged?.Invoke(AttributeType.Fortune, GetTotalAttribute(AttributeType.Fortune));
                OnAttributeChanged?.Invoke(AttributeType.Spirit, GetTotalAttribute(AttributeType.Spirit));
                
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 卸下装备
        /// </summary>
        public bool UnequipCard(Card card)
        {
            if (table.RemoveCard(card))
            {
                card.isEquipped = false;
                hand.AddCard(card);
                
                // 触发属性变化事件
                OnAttributeChanged?.Invoke(AttributeType.Physique, GetTotalAttribute(AttributeType.Physique));
                OnAttributeChanged?.Invoke(AttributeType.Charm, GetTotalAttribute(AttributeType.Charm));
                OnAttributeChanged?.Invoke(AttributeType.Intelligence, GetTotalAttribute(AttributeType.Intelligence));
                OnAttributeChanged?.Invoke(AttributeType.Fortune, GetTotalAttribute(AttributeType.Fortune));
                OnAttributeChanged?.Invoke(AttributeType.Spirit, GetTotalAttribute(AttributeType.Spirit));
                
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 抽卡
        /// </summary>
        public Card DrawCard()
        {
            var card = deck.DrawCard();
            if (card != null)
            {
                hand.AddCard(card);
            }
            return card;
        }
        
        /// <summary>
        /// 弃牌
        /// </summary>
        public bool DiscardCard(Card card)
        {
            if (hand.RemoveCard(card))
            {
                graveyard.AddCard(card);
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 添加初始卡牌
        /// </summary>
        private void AddInitialCards()
        {
            // 添加一些基础卡牌到卡组
            for (int i = 0; i < 20; i++)
            {
                var card = CreateBasicCard(i);
                deck.AddCard(card);
            }
            
            // 抽取初始手牌
            for (int i = 0; i < 7; i++)
            {
                DrawCard();
            }
        }
        
        /// <summary>
        /// 创建基础卡牌
        /// </summary>
        private Card CreateBasicCard(int index)
        {
            var card = new Card
            {
                id = 1000 + index,
                name = $"基础卡牌 {index + 1}",
                description = "一张基础的卡牌",
                cardType = CardType.Character,
                rarity = CardRarity.Common,
                physique = UnityEngine.Random.Range(1, 6),
                charm = UnityEngine.Random.Range(1, 6),
                intelligence = UnityEngine.Random.Range(1, 6),
                fortune = UnityEngine.Random.Range(1, 6),
                spirit = UnityEngine.Random.Range(1, 6)
            };
            
            return card;
        }
        
        /// <summary>
        /// 保存玩家数据
        /// </summary>
        public string SaveToJson()
        {
            return JsonUtility.ToJson(this, true);
        }
        
        /// <summary>
        /// 从JSON加载玩家数据
        /// </summary>
        public static Player LoadFromJson(string json)
        {
            return JsonUtility.FromJson<Player>(json);
        }
    }
    
    /// <summary>
    /// 游戏难度
    /// </summary>
    public enum GameDifficulty
    {
        Easy,       // 简单
        Normal,     // 普通
        Hard,       // 困难
        Expert      // 专家
    }
    
    /// <summary>
    /// 成就系统
    /// </summary>
    [Serializable]
    public class Achievement
    {
        public int id;
        public string name;
        public string description;
        public bool isUnlocked;
        public DateTime unlockedTime;
    }
}
