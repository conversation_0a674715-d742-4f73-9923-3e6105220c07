# 苏丹的游戏 - 完整框架

## 🎮 项目概述

这是一个基于Unity开发的完整卡牌叙事游戏框架，复刻了苏丹的游戏的核心机制和设计理念。框架采用模块化设计，包含完整的游戏系统、数据管理、事件系统和UI框架。

## 🏗️ 架构设计

### 核心系统架构
```
SultansGame_Framework/
├── Core/                   # 核心数据模型
│   ├── Card.cs            # 卡牌数据模型
│   ├── Player.cs          # 玩家数据模型
│   └── CardCollection.cs  # 卡牌集合管理
├── Events/                 # 事件系统
│   └── GameEventSystem.cs # 游戏事件管理
├── Systems/               # 游戏系统
│   ├── RiteSystem.cs      # 仪式系统
│   ├── QuestSystem.cs     # 任务系统
│   └── CardSystem.cs      # 卡牌系统
├── Managers/              # 管理器
│   ├── SultansGameManager.cs # 主游戏管理器
│   ├── DataManager.cs     # 数据管理器
│   └── AudioManager.cs    # 音频管理器
├── Data/                  # 数据配置
│   ├── RiteData.cs        # 仪式数据
│   ├── QuestData.cs       # 任务数据
│   └── GameConfig.cs      # 游戏配置
└── README.md              # 说明文档
```

## 🚀 核心功能

### 1. 卡牌系统 🃏
- **完整的卡牌数据模型**：包含属性、效果、标签等
- **卡牌集合管理**：手牌、卡组、桌面、墓地等
- **卡牌升级系统**：经验值和等级机制
- **稀有度系统**：普通、不常见、稀有、史诗、传说
- **卡牌效果系统**：可扩展的效果框架

### 2. 玩家系统 👤
- **属性系统**：体魄、魅力、智慧、财富、精神
- **等级和经验**：完整的成长系统
- **生命值系统**：健康状态管理
- **成就系统**：游戏进度追踪

### 3. 仪式系统 🔮
- **仪式数据模型**：完整的仪式配置
- **条件检查**：复杂的完成条件系统
- **奖励系统**：多样化的奖励机制
- **进度追踪**：实时仪式进度监控

### 4. 任务系统 📜
- **任务类型**：教程、主线、支线、日常等
- **目标系统**：多样化的任务目标
- **进度追踪**：自动更新任务进度
- **奖励发放**：完成任务自动给予奖励

### 5. 事件系统 📡
- **类型安全**：基于泛型的事件系统
- **松耦合**：组件间通过事件通信
- **易扩展**：支持自定义事件类型
- **性能优化**：高效的事件分发机制

### 6. 数据管理 💾
- **自动保存**：定时保存游戏进度
- **数据加密**：保护存档数据安全
- **备份机制**：防止数据丢失
- **导入导出**：支持存档迁移

### 7. 音频系统 🔊
- **分层音频**：音乐、音效、语音分离
- **淡入淡出**：平滑的音频过渡
- **事件响应**：根据游戏事件播放音频
- **音量控制**：独立的音量设置

## 🎯 使用方法

### 1. 快速开始

1. **导入框架**
   ```csharp
   // 将SultansGame_Framework文件夹复制到Unity项目的Assets目录
   ```

2. **创建游戏管理器**
   ```csharp
   // 在场景中创建空GameObject
   // 添加SultansGameManager组件
   var gameManager = gameObject.AddComponent<SultansGameManager>();
   ```

3. **初始化系统**
   ```csharp
   // 游戏管理器会自动初始化所有系统
   // 或手动初始化特定系统
   var cardSystem = gameObject.AddComponent<CardSystem>();
   var riteSystem = gameObject.AddComponent<RiteSystem>();
   ```

### 2. 创建卡牌

```csharp
// 创建卡牌实例
var card = new Card
{
    id = 2000001,
    name = "年轻的苏丹",
    cardType = CardType.Character,
    rarity = CardRarity.Legendary,
    physique = 15,
    charm = 20,
    intelligence = 18,
    fortune = 25,
    spirit = 22,
    description = "年轻有为的统治者"
};

// 添加到玩家手牌
player.AddCardToHand(card);
```

### 3. 创建仪式

```csharp
// 创建仪式数据
var rite = new RiteData
{
    id = 3300001,
    name = "力量仪式",
    description = "通过特定的卡牌组合来增强体魄属性",
    riteType = RiteType.AttributeBoost,
    difficulty = RiteDifficulty.Easy,
    requirements = new List<RiteRequirement>
    {
        new RiteRequirement 
        { 
            requirementType = RequirementType.CardWithAttribute, 
            attributeType = AttributeType.Physique, 
            minValue = 10 
        }
    },
    rewards = new List<RiteReward>
    {
        new RiteReward 
        { 
            rewardType = RewardType.AttributeBonus, 
            attributeType = AttributeType.Physique, 
            value = 5 
        }
    }
};

// 开始仪式
RiteSystem.Instance.StartRite(rite.id);
```

### 4. 事件系统使用

```csharp
// 订阅事件
GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);

// 发布事件
GameEventSystem.Publish(new CardEventArgs(card, CardEventType.Selected));

// 事件处理
private void OnCardEvent(CardEventArgs args)
{
    Debug.Log($"卡牌事件: {args.Card.name} - {args.EventType}");
}
```

## 🔧 配置说明

### 游戏配置 (GameConfig)
- **玩家设置**：等级上限、经验值计算等
- **卡牌设置**：手牌上限、卡组大小等
- **仪式设置**：最大活跃仪式数等
- **音频设置**：默认音量等

### 难度设置 (DifficultySettings)
- **经验倍率**：不同难度的经验获取倍率
- **属性成长**：属性增长速度
- **卡牌掉落**：稀有卡牌获取概率

## 🎨 扩展指南

### 1. 添加新卡牌类型

```csharp
// 在CardType枚举中添加新类型
public enum CardType
{
    Character,
    Equipment,
    Event,
    Skill,
    Ritual,
    Story,
    NewType  // 新类型
}
```

### 2. 创建自定义效果

```csharp
public class CustomCardEffect : CardEffect
{
    public override void Apply(Player target, Card source)
    {
        // 实现自定义效果逻辑
        Debug.Log("应用自定义效果");
    }
}
```

### 3. 添加新事件类型

```csharp
public class CustomEventArgs : GameEventArgs
{
    public string CustomData { get; private set; }
    
    public CustomEventArgs(string data)
    {
        CustomData = data;
    }
}

// 使用新事件
GameEventSystem.Subscribe<CustomEventArgs>(OnCustomEvent);
GameEventSystem.Publish(new CustomEventArgs("测试数据"));
```

## 📊 性能优化

### 1. 对象池
- 卡牌UI对象复用
- 音效AudioSource池化
- 粒子效果对象池

### 2. 事件优化
- 避免频繁的事件订阅/取消订阅
- 使用事件缓存减少GC
- 异步事件处理

### 3. 数据优化
- 卡牌数据预加载
- 音频资源按需加载
- 存档数据压缩

## 🐛 调试功能

### 1. 调试模式
```csharp
// 在GameConfig中启用调试模式
gameConfig.debugMode = true;
gameConfig.showDebugInfo = true;
gameConfig.enableCheats = true;
```

### 2. 控制台命令
- `AddCard(id)` - 添加指定卡牌
- `SetLevel(level)` - 设置玩家等级
- `CompleteQuest(id)` - 完成指定任务
- `StartRite(id)` - 开始指定仪式

## 📝 更新日志

### v1.0.0 (2024-12-XX)
- ✅ 完整的卡牌系统
- ✅ 仪式系统实现
- ✅ 任务系统实现
- ✅ 事件系统架构
- ✅ 数据管理系统
- ✅ 音频管理系统
- ✅ UI管理框架

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢苏丹的游戏原作提供的设计灵感
- 感谢Unity社区的技术支持
- 感谢所有贡献者的努力

---

**注意**: 这是一个学习和研究项目，仅用于技术交流和教育目的。
