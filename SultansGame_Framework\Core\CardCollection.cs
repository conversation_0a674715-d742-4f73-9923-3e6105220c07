using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace SultansGameClone.Core
{
    /// <summary>
    /// 卡牌集合管理类 - 管理各种卡牌集合（手牌、卡组、桌面等）
    /// </summary>
    [Serializable]
    public class CardCollection
    {
        [Header("集合信息")]
        public string collectionName;       // 集合名称
        public int maxSize = -1;            // 最大容量（-1表示无限制）
        public bool allowDuplicates = true; // 是否允许重复卡牌
        
        [Header("卡牌数据")]
        [SerializeField]
        private List<Card> cards;           // 卡牌列表
        
        [Header("集合状态")]
        public bool isShuffled = false;     // 是否已洗牌
        public DateTime lastModified;       // 最后修改时间
        
        // 事件
        public event Action<Card> OnCardAdded;
        public event Action<Card> OnCardRemoved;
        public event Action OnCollectionChanged;
        
        // 构造函数
        public CardCollection(string name, int maxCapacity = -1)
        {
            collectionName = name;
            maxSize = maxCapacity;
            cards = new List<Card>();
            lastModified = DateTime.Now;
        }
        
        /// <summary>
        /// 获取卡牌数量
        /// </summary>
        public int Count => cards.Count;
        
        /// <summary>
        /// 是否为空
        /// </summary>
        public bool IsEmpty => cards.Count == 0;
        
        /// <summary>
        /// 是否已满
        /// </summary>
        public bool IsFull => maxSize > 0 && cards.Count >= maxSize;
        
        /// <summary>
        /// 获取所有卡牌
        /// </summary>
        public List<Card> GetCards()
        {
            return new List<Card>(cards);
        }
        
        /// <summary>
        /// 添加卡牌
        /// </summary>
        public bool AddCard(Card card)
        {
            if (card == null) return false;
            
            // 检查容量限制
            if (IsFull)
            {
                Debug.LogWarning($"卡牌集合 {collectionName} 已满，无法添加卡牌 {card.name}");
                return false;
            }
            
            // 检查重复卡牌
            if (!allowDuplicates && ContainsCard(card))
            {
                Debug.LogWarning($"卡牌集合 {collectionName} 不允许重复，卡牌 {card.name} 已存在");
                return false;
            }
            
            cards.Add(card);
            lastModified = DateTime.Now;
            
            OnCardAdded?.Invoke(card);
            OnCollectionChanged?.Invoke();
            
            Debug.Log($"卡牌 {card.name} 已添加到 {collectionName}");
            return true;
        }
        
        /// <summary>
        /// 移除卡牌
        /// </summary>
        public bool RemoveCard(Card card)
        {
            if (card == null || !cards.Contains(card)) return false;
            
            cards.Remove(card);
            lastModified = DateTime.Now;
            
            OnCardRemoved?.Invoke(card);
            OnCollectionChanged?.Invoke();
            
            Debug.Log($"卡牌 {card.name} 已从 {collectionName} 移除");
            return true;
        }
        
        /// <summary>
        /// 根据ID移除卡牌
        /// </summary>
        public bool RemoveCardById(int cardId)
        {
            var card = cards.FirstOrDefault(c => c.id == cardId);
            return RemoveCard(card);
        }
        
        /// <summary>
        /// 根据UID移除卡牌
        /// </summary>
        public bool RemoveCardByUid(string uid)
        {
            var card = cards.FirstOrDefault(c => c.uid == uid);
            return RemoveCard(card);
        }
        
        /// <summary>
        /// 检查是否包含指定卡牌
        /// </summary>
        public bool ContainsCard(Card card)
        {
            return cards.Contains(card);
        }
        
        /// <summary>
        /// 根据ID查找卡牌
        /// </summary>
        public Card FindCardById(int cardId)
        {
            return cards.FirstOrDefault(c => c.id == cardId);
        }
        
        /// <summary>
        /// 根据UID查找卡牌
        /// </summary>
        public Card FindCardByUid(string uid)
        {
            return cards.FirstOrDefault(c => c.uid == uid);
        }
        
        /// <summary>
        /// 根据名称查找卡牌
        /// </summary>
        public List<Card> FindCardsByName(string name)
        {
            return cards.Where(c => c.name.Contains(name)).ToList();
        }
        
        /// <summary>
        /// 根据标签查找卡牌
        /// </summary>
        public List<Card> FindCardsByTag(string tag)
        {
            return cards.Where(c => c.HasTag(tag)).ToList();
        }
        
        /// <summary>
        /// 根据类型查找卡牌
        /// </summary>
        public List<Card> FindCardsByType(CardType cardType)
        {
            return cards.Where(c => c.cardType == cardType).ToList();
        }
        
        /// <summary>
        /// 根据稀有度查找卡牌
        /// </summary>
        public List<Card> FindCardsByRarity(CardRarity rarity)
        {
            return cards.Where(c => c.rarity == rarity).ToList();
        }
        
        /// <summary>
        /// 抽取顶部卡牌
        /// </summary>
        public Card DrawCard()
        {
            if (IsEmpty) return null;
            
            var card = cards[0];
            RemoveCard(card);
            return card;
        }
        
        /// <summary>
        /// 抽取随机卡牌
        /// </summary>
        public Card DrawRandomCard()
        {
            if (IsEmpty) return null;
            
            int randomIndex = UnityEngine.Random.Range(0, cards.Count);
            var card = cards[randomIndex];
            RemoveCard(card);
            return card;
        }
        
        /// <summary>
        /// 抽取多张卡牌
        /// </summary>
        public List<Card> DrawCards(int count)
        {
            var drawnCards = new List<Card>();
            
            for (int i = 0; i < count && !IsEmpty; i++)
            {
                var card = DrawCard();
                if (card != null)
                {
                    drawnCards.Add(card);
                }
            }
            
            return drawnCards;
        }
        
        /// <summary>
        /// 洗牌
        /// </summary>
        public void Shuffle()
        {
            for (int i = 0; i < cards.Count; i++)
            {
                int randomIndex = UnityEngine.Random.Range(i, cards.Count);
                (cards[i], cards[randomIndex]) = (cards[randomIndex], cards[i]);
            }
            
            isShuffled = true;
            lastModified = DateTime.Now;
            OnCollectionChanged?.Invoke();
            
            Debug.Log($"卡牌集合 {collectionName} 已洗牌");
        }
        
        /// <summary>
        /// 清空集合
        /// </summary>
        public void Clear()
        {
            var removedCards = new List<Card>(cards);
            cards.Clear();
            lastModified = DateTime.Now;
            
            foreach (var card in removedCards)
            {
                OnCardRemoved?.Invoke(card);
            }
            
            OnCollectionChanged?.Invoke();
            Debug.Log($"卡牌集合 {collectionName} 已清空");
        }
        
        /// <summary>
        /// 排序卡牌
        /// </summary>
        public void SortCards(CardSortType sortType)
        {
            switch (sortType)
            {
                case CardSortType.ById:
                    cards.Sort((a, b) => a.id.CompareTo(b.id));
                    break;
                case CardSortType.ByName:
                    cards.Sort((a, b) => string.Compare(a.name, b.name, StringComparison.Ordinal));
                    break;
                case CardSortType.ByRarity:
                    cards.Sort((a, b) => a.rarity.CompareTo(b.rarity));
                    break;
                case CardSortType.ByType:
                    cards.Sort((a, b) => a.cardType.CompareTo(b.cardType));
                    break;
                case CardSortType.ByTotalAttributes:
                    cards.Sort((a, b) => b.GetTotalAttributes().CompareTo(a.GetTotalAttributes()));
                    break;
                case CardSortType.ByLevel:
                    cards.Sort((a, b) => b.level.CompareTo(a.level));
                    break;
            }
            
            lastModified = DateTime.Now;
            OnCollectionChanged?.Invoke();
            Debug.Log($"卡牌集合 {collectionName} 已按 {sortType} 排序");
        }
        
        /// <summary>
        /// 获取集合统计信息
        /// </summary>
        public CardCollectionStats GetStats()
        {
            return new CardCollectionStats
            {
                totalCards = cards.Count,
                cardsByType = cards.GroupBy(c => c.cardType).ToDictionary(g => g.Key, g => g.Count()),
                cardsByRarity = cards.GroupBy(c => c.rarity).ToDictionary(g => g.Key, g => g.Count()),
                averageLevel = cards.Count > 0 ? cards.Average(c => c.level) : 0,
                totalAttributes = cards.Sum(c => c.GetTotalAttributes())
            };
        }
        
        /// <summary>
        /// 转移卡牌到另一个集合
        /// </summary>
        public bool TransferCard(Card card, CardCollection targetCollection)
        {
            if (RemoveCard(card))
            {
                return targetCollection.AddCard(card);
            }
            return false;
        }
        
        /// <summary>
        /// 转移所有卡牌到另一个集合
        /// </summary>
        public int TransferAllCards(CardCollection targetCollection)
        {
            int transferredCount = 0;
            var cardsToTransfer = new List<Card>(cards);
            
            foreach (var card in cardsToTransfer)
            {
                if (TransferCard(card, targetCollection))
                {
                    transferredCount++;
                }
            }
            
            return transferredCount;
        }
    }
    
    /// <summary>
    /// 卡牌排序类型
    /// </summary>
    public enum CardSortType
    {
        ById,               // 按ID排序
        ByName,             // 按名称排序
        ByRarity,           // 按稀有度排序
        ByType,             // 按类型排序
        ByTotalAttributes,  // 按总属性排序
        ByLevel             // 按等级排序
    }
    
    /// <summary>
    /// 卡牌集合统计信息
    /// </summary>
    [Serializable]
    public class CardCollectionStats
    {
        public int totalCards;
        public Dictionary<CardType, int> cardsByType;
        public Dictionary<CardRarity, int> cardsByRarity;
        public float averageLevel;
        public int totalAttributes;
    }
}
