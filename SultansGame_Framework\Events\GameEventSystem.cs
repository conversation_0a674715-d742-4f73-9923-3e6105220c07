using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace SultansGameClone.Events
{
    /// <summary>
    /// 游戏事件系统 - 基于观察者模式的事件管理
    /// 提供类型安全的事件订阅和发布机制
    /// </summary>
    public static class GameEventSystem
    {
        // 事件监听器字典
        private static Dictionary<Type, List<Delegate>> eventListeners = new Dictionary<Type, List<Delegate>>();
        
        /// <summary>
        /// 订阅事件
        /// </summary>
        public static void Subscribe<T>(Action<T> listener) where T : GameEventArgs
        {
            Type eventType = typeof(T);
            
            if (!eventListeners.ContainsKey(eventType))
            {
                eventListeners[eventType] = new List<Delegate>();
            }
            
            eventListeners[eventType].Add(listener);
            Debug.Log($"订阅事件: {eventType.Name}");
        }
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        public static void Unsubscribe<T>(Action<T> listener) where T : GameEventArgs
        {
            Type eventType = typeof(T);
            
            if (eventListeners.ContainsKey(eventType))
            {
                eventListeners[eventType].Remove(listener);
                
                if (eventListeners[eventType].Count == 0)
                {
                    eventListeners.Remove(eventType);
                }
                
                Debug.Log($"取消订阅事件: {eventType.Name}");
            }
        }
        
        /// <summary>
        /// 发布事件
        /// </summary>
        public static void Publish<T>(T eventArgs) where T : GameEventArgs
        {
            Type eventType = typeof(T);
            
            if (eventListeners.ContainsKey(eventType))
            {
                foreach (var listener in eventListeners[eventType])
                {
                    try
                    {
                        ((Action<T>)listener)?.Invoke(eventArgs);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"事件处理异常: {e.Message}");
                    }
                }
                
                Debug.Log($"发布事件: {eventType.Name}");
            }
        }
        
        /// <summary>
        /// 清除所有事件监听器
        /// </summary>
        public static void Clear()
        {
            eventListeners.Clear();
            Debug.Log("清除所有事件监听器");
        }
        
        /// <summary>
        /// 获取事件监听器数量
        /// </summary>
        public static int GetListenerCount<T>() where T : GameEventArgs
        {
            Type eventType = typeof(T);
            return eventListeners.ContainsKey(eventType) ? eventListeners[eventType].Count : 0;
        }
    }
    
    /// <summary>
    /// 游戏事件参数基类
    /// </summary>
    public abstract class GameEventArgs : EventArgs
    {
        public DateTime Timestamp { get; private set; }
        public string EventId { get; private set; }
        
        protected GameEventArgs()
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
        }
    }
    
    /// <summary>
    /// 卡牌事件参数
    /// </summary>
    public class CardEventArgs : GameEventArgs
    {
        public Core.Card Card { get; private set; }
        public CardEventType EventType { get; private set; }
        public string Action { get; private set; }
        public object AdditionalData { get; private set; }
        
        public CardEventArgs(Core.Card card, CardEventType eventType, string action = "", object additionalData = null)
        {
            Card = card;
            EventType = eventType;
            Action = action;
            AdditionalData = additionalData;
        }
    }
    
    /// <summary>
    /// 玩家事件参数
    /// </summary>
    public class PlayerEventArgs : GameEventArgs
    {
        public Core.Player Player { get; private set; }
        public PlayerEventType EventType { get; private set; }
        public object OldValue { get; private set; }
        public object NewValue { get; private set; }
        
        public PlayerEventArgs(Core.Player player, PlayerEventType eventType, object oldValue = null, object newValue = null)
        {
            Player = player;
            EventType = eventType;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }
    
    /// <summary>
    /// 仪式事件参数
    /// </summary>
    public class RiteEventArgs : GameEventArgs
    {
        public int RiteId { get; private set; }
        public RiteEventType EventType { get; private set; }
        public bool IsSuccessful { get; private set; }
        public List<Core.Card> RequiredCards { get; private set; }
        public Dictionary<string, object> Results { get; private set; }
        
        public RiteEventArgs(int riteId, RiteEventType eventType, bool isSuccessful = false, 
                           List<Core.Card> requiredCards = null, Dictionary<string, object> results = null)
        {
            RiteId = riteId;
            EventType = eventType;
            IsSuccessful = isSuccessful;
            RequiredCards = requiredCards ?? new List<Core.Card>();
            Results = results ?? new Dictionary<string, object>();
        }
    }
    
    /// <summary>
    /// 任务事件参数
    /// </summary>
    public class QuestEventArgs : GameEventArgs
    {
        public int QuestId { get; private set; }
        public QuestEventType EventType { get; private set; }
        public string QuestName { get; private set; }
        public Dictionary<string, object> QuestData { get; private set; }
        
        public QuestEventArgs(int questId, QuestEventType eventType, string questName = "", 
                            Dictionary<string, object> questData = null)
        {
            QuestId = questId;
            EventType = eventType;
            QuestName = questName;
            QuestData = questData ?? new Dictionary<string, object>();
        }
    }
    
    /// <summary>
    /// UI事件参数
    /// </summary>
    public class UIEventArgs : GameEventArgs
    {
        public string UIElementName { get; private set; }
        public UIEventType EventType { get; private set; }
        public object Data { get; private set; }
        
        public UIEventArgs(string uiElementName, UIEventType eventType, object data = null)
        {
            UIElementName = uiElementName;
            EventType = eventType;
            Data = data;
        }
    }
    
    /// <summary>
    /// 游戏状态事件参数
    /// </summary>
    public class GameStateEventArgs : GameEventArgs
    {
        public GameStateType OldState { get; private set; }
        public GameStateType NewState { get; private set; }
        public string Reason { get; private set; }
        
        public GameStateEventArgs(GameStateType oldState, GameStateType newState, string reason = "")
        {
            OldState = oldState;
            NewState = newState;
            Reason = reason;
        }
    }
    
    // ==================== 事件类型枚举 ====================
    
    /// <summary>
    /// 卡牌事件类型
    /// </summary>
    public enum CardEventType
    {
        Selected,       // 选中
        Deselected,     // 取消选中
        Equipped,       // 装备
        Unequipped,     // 卸下
        Used,           // 使用
        Drawn,          // 抽取
        Discarded,      // 弃置
        LevelUp,        // 升级
        Destroyed       // 销毁
    }
    
    /// <summary>
    /// 玩家事件类型
    /// </summary>
    public enum PlayerEventType
    {
        LevelUp,            // 升级
        HealthChanged,      // 生命值变化
        AttributeChanged,   // 属性变化
        ExperienceGained,   // 获得经验
        CardAdded,          // 添加卡牌
        CardRemoved,        // 移除卡牌
        QuestCompleted,     // 完成任务
        AchievementUnlocked // 解锁成就
    }
    
    /// <summary>
    /// 仪式事件类型
    /// </summary>
    public enum RiteEventType
    {
        Started,        // 开始
        Completed,      // 完成
        Failed,         // 失败
        Cancelled,      // 取消
        RequirementMet, // 满足条件
        RewardGiven     // 给予奖励
    }
    
    /// <summary>
    /// 任务事件类型
    /// </summary>
    public enum QuestEventType
    {
        Started,        // 开始
        Completed,      // 完成
        Failed,         // 失败
        Updated,        // 更新
        Abandoned       // 放弃
    }
    
    /// <summary>
    /// UI事件类型
    /// </summary>
    public enum UIEventType
    {
        Clicked,        // 点击
        Opened,         // 打开
        Closed,         // 关闭
        Updated,        // 更新
        Animated        // 动画
    }
    
    /// <summary>
    /// 游戏状态类型
    /// </summary>
    public enum GameStateType
    {
        Loading,        // 加载中
        MainMenu,       // 主菜单
        Playing,        // 游戏中
        Paused,         // 暂停
        GameOver,       // 游戏结束
        Victory,        // 胜利
        Settings        // 设置
    }
    
    /// <summary>
    /// Unity事件系统兼容类
    /// 为了与现有的UnityEvent系统兼容
    /// </summary>
    public static class GameEvents
    {
        public static UnityEvent<CardEventArgs> OnCardSelected = new UnityEvent<CardEventArgs>();
        public static UnityEvent<CardEventArgs> OnCardMoved = new UnityEvent<CardEventArgs>();
        public static UnityEvent<CardEventArgs> OnCardEquipped = new UnityEvent<CardEventArgs>();
        public static UnityEvent<CardEventArgs> OnCardUnequipped = new UnityEvent<CardEventArgs>();
        public static UnityEvent<PlayerEventArgs> OnPlayerLevelUp = new UnityEvent<PlayerEventArgs>();
        public static UnityEvent<int> OnRoundChanged = new UnityEvent<int>();
        public static UnityEvent<string, int> OnAttributeChanged = new UnityEvent<string, int>();
        public static UnityEvent<RiteEventArgs> OnRiteCompleted = new UnityEvent<RiteEventArgs>();
        public static UnityEvent<QuestEventArgs> OnQuestCompleted = new UnityEvent<QuestEventArgs>();
        public static UnityEvent<GameStateEventArgs> OnGameStateChanged = new UnityEvent<GameStateEventArgs>();
    }
}
