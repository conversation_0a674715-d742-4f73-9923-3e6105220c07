using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using SultansGameClone.Events;

namespace SultansGameClone.Managers
{
    /// <summary>
    /// 音频管理器 - 管理游戏中的音乐和音效
    /// </summary>
    public class AudioManager : MonoBehaviour
    {
        [Header("音频源")]
        public AudioSource musicSource;
        public AudioSource sfxSource;
        public AudioSource voiceSource;
        
        [Header("音量设置")]
        [Range(0f, 1f)] public float masterVolume = 1f;
        [Range(0f, 1f)] public float musicVolume = 0.7f;
        [Range(0f, 1f)] public float sfxVolume = 0.8f;
        [Range(0f, 1f)] public float voiceVolume = 1f;
        
        [Header("音频资源")]
        public AudioClipDatabase audioDatabase;
        
        [Header("淡入淡出")]
        public float fadeTime = 1f;
        
        // 单例
        public static AudioManager Instance { get; private set; }
        
        // 音频缓存
        private Dictionary<string, AudioClip> audioCache = new Dictionary<string, AudioClip>();
        private Dictionary<string, AudioSource> activeSources = new Dictionary<string, AudioSource>();
        
        // 当前播放状态
        private string currentMusicTrack;
        private Coroutine musicFadeCoroutine;
        
        // 事件
        public event Action<string> OnMusicChanged;
        public event Action<string> OnSfxPlayed;
        public event Action<float> OnVolumeChanged;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudioManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 初始化音频管理器
        /// </summary>
        private void InitializeAudioManager()
        {
            Debug.Log("🔊 初始化音频管理器...");
            
            // 创建音频源（如果不存在）
            CreateAudioSources();
            
            // 加载音频数据库
            LoadAudioDatabase();
            
            // 订阅游戏事件
            GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Subscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Subscribe<RiteEventArgs>(OnRiteEvent);
            GameEventSystem.Subscribe<QuestEventArgs>(OnQuestEvent);
            GameEventSystem.Subscribe<GameStateEventArgs>(OnGameStateEvent);
            
            // 应用音量设置
            ApplyVolumeSettings();
            
            Debug.Log("✅ 音频管理器初始化完成");
        }
        
        /// <summary>
        /// 创建音频源
        /// </summary>
        private void CreateAudioSources()
        {
            if (musicSource == null)
            {
                var musicGO = new GameObject("Music Source");
                musicGO.transform.SetParent(transform);
                musicSource = musicGO.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.playOnAwake = false;
            }
            
            if (sfxSource == null)
            {
                var sfxGO = new GameObject("SFX Source");
                sfxGO.transform.SetParent(transform);
                sfxSource = sfxGO.AddComponent<AudioSource>();
                sfxSource.loop = false;
                sfxSource.playOnAwake = false;
            }
            
            if (voiceSource == null)
            {
                var voiceGO = new GameObject("Voice Source");
                voiceGO.transform.SetParent(transform);
                voiceSource = voiceGO.AddComponent<AudioSource>();
                voiceSource.loop = false;
                voiceSource.playOnAwake = false;
            }
        }
        
        /// <summary>
        /// 加载音频数据库
        /// </summary>
        private void LoadAudioDatabase()
        {
            if (audioDatabase == null)
            {
                audioDatabase = Resources.Load<AudioClipDatabase>("AudioDatabase");
                if (audioDatabase == null)
                {
                    Debug.LogWarning("未找到音频数据库");
                    return;
                }
            }
            
            // 预加载常用音频
            PreloadCommonAudio();
        }
        
        /// <summary>
        /// 预加载常用音频
        /// </summary>
        private void PreloadCommonAudio()
        {
            if (audioDatabase?.commonAudioClips == null) return;
            
            foreach (var audioClip in audioDatabase.commonAudioClips)
            {
                if (audioClip.clip != null)
                {
                    audioCache[audioClip.name] = audioClip.clip;
                }
            }
            
            Debug.Log($"🔊 预加载音频: {audioCache.Count} 个");
        }
        
        /// <summary>
        /// 播放音乐
        /// </summary>
        public void PlayMusic(string trackName, bool fadeIn = true)
        {
            var clip = GetAudioClip(trackName);
            if (clip == null)
            {
                Debug.LogWarning($"未找到音乐: {trackName}");
                return;
            }
            
            if (currentMusicTrack == trackName && musicSource.isPlaying)
            {
                return; // 已经在播放相同的音乐
            }
            
            if (musicFadeCoroutine != null)
            {
                StopCoroutine(musicFadeCoroutine);
            }
            
            if (fadeIn && musicSource.isPlaying)
            {
                musicFadeCoroutine = StartCoroutine(FadeToNewMusic(clip, trackName));
            }
            else
            {
                musicSource.clip = clip;
                musicSource.Play();
                currentMusicTrack = trackName;
            }
            
            OnMusicChanged?.Invoke(trackName);
            Debug.Log($"🎵 播放音乐: {trackName}");
        }
        
        /// <summary>
        /// 停止音乐
        /// </summary>
        public void StopMusic(bool fadeOut = true)
        {
            if (fadeOut && musicSource.isPlaying)
            {
                if (musicFadeCoroutine != null)
                {
                    StopCoroutine(musicFadeCoroutine);
                }
                musicFadeCoroutine = StartCoroutine(FadeOutMusic());
            }
            else
            {
                musicSource.Stop();
                currentMusicTrack = null;
            }
        }
        
        /// <summary>
        /// 播放音效
        /// </summary>
        public void PlaySFX(string sfxName, float volumeScale = 1f)
        {
            var clip = GetAudioClip(sfxName);
            if (clip == null)
            {
                Debug.LogWarning($"未找到音效: {sfxName}");
                return;
            }
            
            sfxSource.PlayOneShot(clip, volumeScale);
            OnSfxPlayed?.Invoke(sfxName);
        }
        
        /// <summary>
        /// 播放语音
        /// </summary>
        public void PlayVoice(string voiceName, float volumeScale = 1f)
        {
            var clip = GetAudioClip(voiceName);
            if (clip == null)
            {
                Debug.LogWarning($"未找到语音: {voiceName}");
                return;
            }
            
            voiceSource.clip = clip;
            voiceSource.volume = voiceVolume * masterVolume * volumeScale;
            voiceSource.Play();
        }
        
        /// <summary>
        /// 获取音频剪辑
        /// </summary>
        private AudioClip GetAudioClip(string clipName)
        {
            // 先从缓存查找
            if (audioCache.ContainsKey(clipName))
            {
                return audioCache[clipName];
            }
            
            // 从数据库查找
            if (audioDatabase != null)
            {
                var audioData = audioDatabase.GetAudioClip(clipName);
                if (audioData?.clip != null)
                {
                    audioCache[clipName] = audioData.clip;
                    return audioData.clip;
                }
            }
            
            // 尝试从Resources加载
            var clip = Resources.Load<AudioClip>($"Audio/{clipName}");
            if (clip != null)
            {
                audioCache[clipName] = clip;
                return clip;
            }
            
            return null;
        }
        
        /// <summary>
        /// 设置主音量
        /// </summary>
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            ApplyVolumeSettings();
            OnVolumeChanged?.Invoke(masterVolume);
        }
        
        /// <summary>
        /// 设置音乐音量
        /// </summary>
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            ApplyVolumeSettings();
        }
        
        /// <summary>
        /// 设置音效音量
        /// </summary>
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            ApplyVolumeSettings();
        }
        
        /// <summary>
        /// 设置语音音量
        /// </summary>
        public void SetVoiceVolume(float volume)
        {
            voiceVolume = Mathf.Clamp01(volume);
            ApplyVolumeSettings();
        }
        
        /// <summary>
        /// 应用音量设置
        /// </summary>
        private void ApplyVolumeSettings()
        {
            if (musicSource != null)
                musicSource.volume = musicVolume * masterVolume;
            
            if (sfxSource != null)
                sfxSource.volume = sfxVolume * masterVolume;
            
            if (voiceSource != null)
                voiceSource.volume = voiceVolume * masterVolume;
        }
        
        /// <summary>
        /// 淡入淡出到新音乐
        /// </summary>
        private IEnumerator FadeToNewMusic(AudioClip newClip, string trackName)
        {
            float startVolume = musicSource.volume;
            
            // 淡出当前音乐
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                musicSource.volume = Mathf.Lerp(startVolume, 0, t / fadeTime);
                yield return null;
            }
            
            // 切换音乐
            musicSource.clip = newClip;
            musicSource.Play();
            currentMusicTrack = trackName;
            
            // 淡入新音乐
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                musicSource.volume = Mathf.Lerp(0, musicVolume * masterVolume, t / fadeTime);
                yield return null;
            }
            
            musicSource.volume = musicVolume * masterVolume;
            musicFadeCoroutine = null;
        }
        
        /// <summary>
        /// 淡出音乐
        /// </summary>
        private IEnumerator FadeOutMusic()
        {
            float startVolume = musicSource.volume;
            
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                musicSource.volume = Mathf.Lerp(startVolume, 0, t / fadeTime);
                yield return null;
            }
            
            musicSource.Stop();
            musicSource.volume = musicVolume * masterVolume;
            currentMusicTrack = null;
            musicFadeCoroutine = null;
        }
        
        // ==================== 事件处理 ====================
        
        private void OnCardEvent(CardEventArgs args)
        {
            switch (args.EventType)
            {
                case CardEventType.Selected:
                    PlaySFX("card_select");
                    break;
                case CardEventType.Equipped:
                    PlaySFX("card_equip");
                    break;
                case CardEventType.Used:
                    PlaySFX("card_use");
                    break;
                case CardEventType.LevelUp:
                    PlaySFX("card_levelup");
                    break;
            }
        }
        
        private void OnPlayerEvent(PlayerEventArgs args)
        {
            switch (args.EventType)
            {
                case PlayerEventType.LevelUp:
                    PlaySFX("player_levelup");
                    break;
                case PlayerEventType.HealthChanged:
                    if ((int)args.NewValue < (int)args.OldValue)
                        PlaySFX("player_damage");
                    else
                        PlaySFX("player_heal");
                    break;
            }
        }
        
        private void OnRiteEvent(RiteEventArgs args)
        {
            switch (args.EventType)
            {
                case RiteEventType.Started:
                    PlaySFX("rite_start");
                    break;
                case RiteEventType.Completed:
                    PlaySFX("rite_complete");
                    break;
                case RiteEventType.Failed:
                    PlaySFX("rite_fail");
                    break;
            }
        }
        
        private void OnQuestEvent(QuestEventArgs args)
        {
            switch (args.EventType)
            {
                case QuestEventType.Started:
                    PlaySFX("quest_start");
                    break;
                case QuestEventType.Completed:
                    PlaySFX("quest_complete");
                    break;
                case QuestEventType.Updated:
                    PlaySFX("quest_update");
                    break;
            }
        }
        
        private void OnGameStateEvent(GameStateEventArgs args)
        {
            switch (args.NewState)
            {
                case GameStateType.MainMenu:
                    PlayMusic("main_menu");
                    break;
                case GameStateType.Playing:
                    PlayMusic("gameplay");
                    break;
                case GameStateType.GameOver:
                    PlayMusic("game_over");
                    break;
            }
        }
        
        private void OnDestroy()
        {
            GameEventSystem.Unsubscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Unsubscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Unsubscribe<RiteEventArgs>(OnRiteEvent);
            GameEventSystem.Unsubscribe<QuestEventArgs>(OnQuestEvent);
            GameEventSystem.Unsubscribe<GameStateEventArgs>(OnGameStateEvent);
        }
    }
    
    /// <summary>
    /// 音频数据
    /// </summary>
    [Serializable]
    public class AudioData
    {
        public string name;
        public AudioClip clip;
        public float volume = 1f;
        public bool loop = false;
    }
    
    /// <summary>
    /// 音频剪辑数据库
    /// </summary>
    [CreateAssetMenu(fileName = "AudioDatabase", menuName = "Sultan's Game/Audio Database")]
    public class AudioClipDatabase : ScriptableObject
    {
        public List<AudioData> commonAudioClips = new List<AudioData>();
        public List<AudioData> musicTracks = new List<AudioData>();
        public List<AudioData> soundEffects = new List<AudioData>();
        public List<AudioData> voiceClips = new List<AudioData>();
        
        public AudioData GetAudioClip(string clipName)
        {
            var allClips = new List<AudioData>();
            allClips.AddRange(commonAudioClips);
            allClips.AddRange(musicTracks);
            allClips.AddRange(soundEffects);
            allClips.AddRange(voiceClips);
            
            return allClips.Find(clip => clip.name == clipName);
        }
    }
}
