using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

namespace SultansGameClone
{
    // ==================== UI管理器 ====================
    
    public class UIManager : MonoBehaviour
    {
        [Header("主要UI面板")]
        public GameObject mainMenuPanel;
        public GameObject gameHUDPanel;
        public GameObject inventoryPanel;
        public GameObject settingsPanel;
        public GameObject cardDetailPanel;
        
        [Header("游戏HUD组件")]
        public Text playerNameText;
        public Text playerLevelText;
        public Text roundText;
        public Slider experienceSlider;
        
        [Header("属性显示")]
        public Text physiqueText;
        public Text charmText;
        public Text intelligenceText;
        public Text fortuneText;
        public Text spiritText;
        
        [Header("按钮")]
        public Button nextRoundButton;
        public Button inventoryButton;
        public Button settingsButton;
        public Button saveButton;
        public Button loadButton;
        
        [Header("卡牌详情面板")]
        public Image detailCardImage;
        public Text detailNameText;
        public Text detailDescriptionText;
        public Text[] detailAttributeTexts = new Text[5];
        public Button equipButton;
        public Button closeDetailButton;
        
        // 单例模式
        public static UIManager Instance { get; private set; }
        
        // 当前显示的面板
        private GameObject currentPanel;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeUI();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            // 订阅游戏事件
            GameEvents.OnCardSelected.AddListener(OnCardSelected);
            GameEvents.OnPlayerLevelUp.AddListener(OnPlayerLevelUp);
            GameEvents.OnRoundChanged.AddListener(OnRoundChanged);
            GameEvents.OnAttributeChanged.AddListener(OnAttributeChanged);
            
            // 绑定按钮事件
            BindButtonEvents();
            
            // 显示主菜单
            ShowMainMenu();
        }
        
        private void OnDestroy()
        {
            // 取消订阅事件
            GameEvents.OnCardSelected.RemoveListener(OnCardSelected);
            GameEvents.OnPlayerLevelUp.RemoveListener(OnPlayerLevelUp);
            GameEvents.OnRoundChanged.RemoveListener(OnRoundChanged);
            GameEvents.OnAttributeChanged.RemoveListener(OnAttributeChanged);
        }
        
        // 初始化UI
        private void InitializeUI()
        {
            Debug.Log("🎨 初始化UI管理器...");
            
            // 隐藏所有面板
            HideAllPanels();
            
            // 初始化卡牌详情面板
            if (cardDetailPanel != null)
                cardDetailPanel.SetActive(false);
        }
        
        // 绑定按钮事件
        private void BindButtonEvents()
        {
            if (nextRoundButton != null)
                nextRoundButton.onClick.AddListener(OnNextRoundClicked);
            
            if (inventoryButton != null)
                inventoryButton.onClick.AddListener(OnInventoryClicked);
            
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);
            
            if (saveButton != null)
                saveButton.onClick.AddListener(OnSaveClicked);
            
            if (loadButton != null)
                loadButton.onClick.AddListener(OnLoadClicked);
            
            if (equipButton != null)
                equipButton.onClick.AddListener(OnEquipClicked);
            
            if (closeDetailButton != null)
                closeDetailButton.onClick.AddListener(OnCloseDetailClicked);
        }
        
        // ==================== 面板管理 ====================
        
        // 显示主菜单
        public void ShowMainMenu()
        {
            ShowPanel(mainMenuPanel);
        }
        
        // 显示游戏HUD
        public void ShowGameHUD()
        {
            ShowPanel(gameHUDPanel);
            UpdateGameHUD();
        }
        
        // 显示库存面板
        public void ShowInventory()
        {
            ShowPanel(inventoryPanel);
        }
        
        // 显示设置面板
        public void ShowSettings()
        {
            ShowPanel(settingsPanel);
        }
        
        // 显示指定面板
        private void ShowPanel(GameObject panel)
        {
            if (panel == null) return;
            
            // 隐藏当前面板
            if (currentPanel != null && currentPanel != panel)
            {
                HidePanel(currentPanel);
            }
            
            // 显示新面板
            panel.SetActive(true);
            currentPanel = panel;
            
            // 播放显示动画
            PlayShowAnimation(panel);
            
            Debug.Log($"📱 显示面板: {panel.name}");
        }
        
        // 隐藏面板
        private void HidePanel(GameObject panel)
        {
            if (panel == null) return;
            
            // 播放隐藏动画
            PlayHideAnimation(panel, () => {
                panel.SetActive(false);
            });
        }
        
        // 隐藏所有面板
        private void HideAllPanels()
        {
            GameObject[] panels = { mainMenuPanel, gameHUDPanel, inventoryPanel, settingsPanel };
            
            foreach (var panel in panels)
            {
                if (panel != null)
                    panel.SetActive(false);
            }
            
            currentPanel = null;
        }
        
        // ==================== 动画效果 ====================
        
        // 播放显示动画
        private void PlayShowAnimation(GameObject panel)
        {
            if (panel == null) return;
            
            // 淡入动画
            CanvasGroup canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = panel.AddComponent<CanvasGroup>();
            
            canvasGroup.alpha = 0f;
            canvasGroup.DOFade(1f, 0.3f).SetEase(Ease.OutQuart);
            
            // 缩放动画
            panel.transform.localScale = Vector3.zero;
            panel.transform.DOScale(Vector3.one, 0.3f).SetEase(Ease.OutBack);
        }
        
        // 播放隐藏动画
        private void PlayHideAnimation(GameObject panel, System.Action onComplete)
        {
            if (panel == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            // 淡出动画
            CanvasGroup canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
            {
                canvasGroup.DOFade(0f, 0.2f).SetEase(Ease.InQuart).OnComplete(() => onComplete?.Invoke());
            }
            else
            {
                onComplete?.Invoke();
            }
        }
        
        // ==================== 游戏HUD更新 ====================
        
        // 更新游戏HUD
        public void UpdateGameHUD()
        {
            if (GameManager.Instance == null || GameManager.Instance.currentPlayer == null) return;
            
            var player = GameManager.Instance.currentPlayer;
            
            // 更新玩家信息
            if (playerNameText != null)
                playerNameText.text = player.playerName;
            
            if (playerLevelText != null)
                playerLevelText.text = $"等级 {player.level}";
            
            if (roundText != null)
                roundText.text = $"第 {player.round} 回合";
            
            // 更新经验条
            if (experienceSlider != null)
            {
                int requiredExp = player.level * 100;
                experienceSlider.value = (float)player.experience / requiredExp;
            }
            
            // 更新属性显示
            UpdateAttributeDisplay();
        }
        
        // 更新属性显示
        private void UpdateAttributeDisplay()
        {
            if (GameManager.Instance == null) return;
            
            var player = GameManager.Instance.currentPlayer;
            var tableCards = GameManager.Instance.table;
            
            // 计算总属性
            int totalPhysique = player.GetTotalAttribute("physique", tableCards);
            int totalCharm = player.GetTotalAttribute("charm", tableCards);
            int totalIntelligence = player.GetTotalAttribute("intelligence", tableCards);
            int totalFortune = player.GetTotalAttribute("fortune", tableCards);
            int totalSpirit = player.GetTotalAttribute("spirit", tableCards);
            
            // 更新UI文本
            if (physiqueText != null)
                physiqueText.text = totalPhysique.ToString();
            
            if (charmText != null)
                charmText.text = totalCharm.ToString();
            
            if (intelligenceText != null)
                intelligenceText.text = totalIntelligence.ToString();
            
            if (fortuneText != null)
                fortuneText.text = totalFortune.ToString();
            
            if (spiritText != null)
                spiritText.text = totalSpirit.ToString();
        }
        
        // ==================== 卡牌详情面板 ====================
        
        // 显示卡牌详情
        public void ShowCardDetail(Card card)
        {
            if (card == null || cardDetailPanel == null) return;
            
            // 更新详情内容
            if (detailNameText != null)
                detailNameText.text = card.name;
            
            if (detailDescriptionText != null)
                detailDescriptionText.text = card.description;
            
            // 更新属性显示
            int[] attributes = { card.physique, card.charm, card.intelligence, card.fortune, card.spirit };
            for (int i = 0; i < detailAttributeTexts.Length && i < attributes.Length; i++)
            {
                if (detailAttributeTexts[i] != null)
                    detailAttributeTexts[i].text = attributes[i].ToString();
            }
            
            // 更新装备按钮文本
            if (equipButton != null)
            {
                Text buttonText = equipButton.GetComponentInChildren<Text>();
                if (buttonText != null)
                    buttonText.text = card.isEquipped ? "卸下" : "装备";
            }
            
            // 显示面板
            cardDetailPanel.SetActive(true);
            PlayShowAnimation(cardDetailPanel);
        }
        
        // 隐藏卡牌详情
        public void HideCardDetail()
        {
            if (cardDetailPanel == null) return;
            
            PlayHideAnimation(cardDetailPanel, () => {
                cardDetailPanel.SetActive(false);
            });
        }
        
        // ==================== 事件处理 ====================
        
        // 卡牌选择事件
        private void OnCardSelected(CardEventArgs args)
        {
            if (args.card != null)
            {
                ShowCardDetail(args.card);
            }
        }
        
        // 玩家升级事件
        private void OnPlayerLevelUp(Player player)
        {
            UpdateGameHUD();
            
            // 显示升级特效
            ShowLevelUpEffect();
        }
        
        // 回合变化事件
        private void OnRoundChanged(int round)
        {
            if (roundText != null)
                roundText.text = $"第 {round} 回合";
        }
        
        // 属性变化事件
        private void OnAttributeChanged(string attributeName, int newValue)
        {
            UpdateAttributeDisplay();
        }
        
        // 显示升级特效
        private void ShowLevelUpEffect()
        {
            // TODO: 实现升级特效
            Debug.Log("🎉 播放升级特效!");
        }
        
        // ==================== 按钮事件 ====================
        
        private void OnNextRoundClicked()
        {
            GameManager.Instance.NextRound();
        }
        
        private void OnInventoryClicked()
        {
            ShowInventory();
        }
        
        private void OnSettingsClicked()
        {
            ShowSettings();
        }
        
        private void OnSaveClicked()
        {
            GameManager.Instance.SaveGame();
        }
        
        private void OnLoadClicked()
        {
            GameManager.Instance.LoadGame();
        }
        
        private void OnEquipClicked()
        {
            if (GameManager.Instance.selectedCard != null)
            {
                var card = GameManager.Instance.selectedCard;
                if (card.isEquipped)
                {
                    GameManager.Instance.UnequipCard(card);
                }
                else
                {
                    GameManager.Instance.EquipCard(card);
                }
                
                // 更新详情面板
                ShowCardDetail(card);
            }
        }
        
        private void OnCloseDetailClicked()
        {
            HideCardDetail();
        }
    }
}
