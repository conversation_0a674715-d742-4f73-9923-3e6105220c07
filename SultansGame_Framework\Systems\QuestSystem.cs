using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using SultansGameClone.Core;
using SultansGameClone.Events;

namespace SultansGameClone.Systems
{
    /// <summary>
    /// 任务系统 - 管理游戏中的任务和剧情进度
    /// </summary>
    public class QuestSystem : MonoBehaviour
    {
        [Header("任务配置")]
        public List<QuestData> allQuests = new List<QuestData>();
        public int maxActiveQuests = 5;
        
        [Header("当前状态")]
        public List<QuestData> activeQuests = new List<QuestData>();
        public List<QuestData> completedQuests = new List<QuestData>();
        public List<QuestData> availableQuests = new List<QuestData>();
        
        [Header("主线任务")]
        public QuestData currentMainQuest;
        public int mainQuestProgress = 0;
        
        // 单例
        public static QuestSystem Instance { get; private set; }
        
        // 事件
        public event Action<QuestData> OnQuestStarted;
        public event Action<QuestData> OnQuestCompleted;
        public event Action<QuestData> OnQuestFailed;
        public event Action<QuestData> OnQuestUpdated;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeQuestSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 初始化任务系统
        /// </summary>
        private void InitializeQuestSystem()
        {
            Debug.Log("📜 初始化任务系统...");
            
            // 加载任务数据
            LoadQuestData();
            
            // 订阅游戏事件
            GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Subscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Subscribe<RiteEventArgs>(OnRiteEvent);
            
            // 初始化可用任务
            UpdateAvailableQuests();
            
            Debug.Log($"✅ 任务系统初始化完成 - 总任务数: {allQuests.Count}");
        }
        
        /// <summary>
        /// 加载任务数据
        /// </summary>
        private void LoadQuestData()
        {
            CreateSampleQuests();
        }
        
        /// <summary>
        /// 创建示例任务
        /// </summary>
        private void CreateSampleQuests()
        {
            // 新手引导任务
            var tutorialQuest = new QuestData
            {
                id = 1001,
                questName = "初入宫廷",
                description = "欢迎来到苏丹的宫廷！让我们开始你的冒险之旅。",
                questType = QuestType.Tutorial,
                isMainQuest = true,
                objectives = new List<QuestObjective>
                {
                    new QuestObjective { description = "装备一张卡牌", objectiveType = ObjectiveType.EquipCard, targetValue = 1 },
                    new QuestObjective { description = "达到等级2", objectiveType = ObjectiveType.ReachLevel, targetValue = 2 }
                },
                rewards = new List<QuestReward>
                {
                    new QuestReward { rewardType = QuestRewardType.Experience, value = 100 },
                    new QuestReward { rewardType = QuestRewardType.DrawCard, value = 3 }
                }
            };
            
            // 属性提升任务
            var attributeQuest = new QuestData
            {
                id = 1002,
                questName = "力量的试炼",
                description = "通过训练和装备来提升你的体魄属性。",
                questType = QuestType.Attribute,
                objectives = new List<QuestObjective>
                {
                    new QuestObjective { description = "体魄属性达到20点", objectiveType = ObjectiveType.ReachAttribute, 
                                       attributeType = AttributeType.Physique, targetValue = 20 }
                },
                rewards = new List<QuestReward>
                {
                    new QuestReward { rewardType = QuestRewardType.AttributeBonus, attributeType = AttributeType.Physique, value = 5 },
                    new QuestReward { rewardType = QuestRewardType.UnlockCard, value = 2001 }
                }
            };
            
            // 仪式任务
            var riteQuest = new QuestData
            {
                id = 1003,
                questName = "神秘仪式",
                description = "完成你的第一个仪式，感受神秘力量的召唤。",
                questType = QuestType.Ritual,
                objectives = new List<QuestObjective>
                {
                    new QuestObjective { description = "完成任意一个仪式", objectiveType = ObjectiveType.CompleteRite, targetValue = 1 }
                },
                rewards = new List<QuestReward>
                {
                    new QuestReward { rewardType = QuestRewardType.Experience, value = 200 },
                    new QuestReward { rewardType = QuestRewardType.UnlockRite, value = 3300002 }
                }
            };
            
            // 收集任务
            var collectionQuest = new QuestData
            {
                id = 1004,
                questName = "卡牌收藏家",
                description = "收集不同类型的卡牌来丰富你的收藏。",
                questType = QuestType.Collection,
                objectives = new List<QuestObjective>
                {
                    new QuestObjective { description = "拥有10张不同的卡牌", objectiveType = ObjectiveType.CollectCards, targetValue = 10 }
                },
                rewards = new List<QuestReward>
                {
                    new QuestReward { rewardType = QuestRewardType.DrawCard, value = 5 },
                    new QuestReward { rewardType = QuestRewardType.Experience, value = 150 }
                }
            };
            
            allQuests.AddRange(new[] { tutorialQuest, attributeQuest, riteQuest, collectionQuest });
        }
        
        /// <summary>
        /// 开始任务
        /// </summary>
        public bool StartQuest(int questId)
        {
            var quest = allQuests.FirstOrDefault(q => q.id == questId);
            if (quest == null)
            {
                Debug.LogWarning($"未找到任务 ID: {questId}");
                return false;
            }
            
            if (activeQuests.Count >= maxActiveQuests)
            {
                Debug.LogWarning("活跃任务数量已达上限");
                return false;
            }
            
            if (activeQuests.Any(q => q.id == questId))
            {
                Debug.LogWarning($"任务 {quest.questName} 已经在进行中");
                return false;
            }
            
            if (!quest.CanStart())
            {
                Debug.LogWarning($"不满足任务 {quest.questName} 的开始条件");
                return false;
            }
            
            activeQuests.Add(quest);
            availableQuests.Remove(quest);
            quest.isActive = true;
            quest.startTime = DateTime.Now;
            
            // 如果是主线任务
            if (quest.isMainQuest)
            {
                currentMainQuest = quest;
            }
            
            OnQuestStarted?.Invoke(quest);
            GameEventSystem.Publish(new QuestEventArgs(questId, QuestEventType.Started, quest.questName));
            
            Debug.Log($"📜 开始任务: {quest.questName}");
            return true;
        }
        
        /// <summary>
        /// 更新任务进度
        /// </summary>
        public void UpdateQuestProgress()
        {
            var player = GameManager.Instance?.currentPlayer;
            if (player == null) return;
            
            var questsToComplete = new List<QuestData>();
            
            foreach (var quest in activeQuests)
            {
                bool wasCompleted = quest.isCompleted;
                quest.UpdateProgress(player);
                
                if (!wasCompleted && quest.isCompleted)
                {
                    questsToComplete.Add(quest);
                }
                else if (quest.HasProgressChanged())
                {
                    OnQuestUpdated?.Invoke(quest);
                    GameEventSystem.Publish(new QuestEventArgs(quest.id, QuestEventType.Updated, quest.questName));
                }
            }
            
            foreach (var quest in questsToComplete)
            {
                CompleteQuest(quest);
            }
        }
        
        /// <summary>
        /// 完成任务
        /// </summary>
        private void CompleteQuest(QuestData quest)
        {
            activeQuests.Remove(quest);
            completedQuests.Add(quest);
            
            quest.isActive = false;
            quest.isCompleted = true;
            quest.completionTime = DateTime.Now;
            
            // 给予奖励
            GiveQuestRewards(quest);
            
            // 解锁后续任务
            UnlockFollowUpQuests(quest);
            
            OnQuestCompleted?.Invoke(quest);
            GameEventSystem.Publish(new QuestEventArgs(quest.id, QuestEventType.Completed, quest.questName));
            
            Debug.Log($"✅ 完成任务: {quest.questName}");
        }
        
        /// <summary>
        /// 给予任务奖励
        /// </summary>
        private void GiveQuestRewards(QuestData quest)
        {
            var player = GameManager.Instance?.currentPlayer;
            if (player == null) return;
            
            foreach (var reward in quest.rewards)
            {
                ApplyQuestReward(reward, player);
            }
        }
        
        /// <summary>
        /// 应用任务奖励
        /// </summary>
        private void ApplyQuestReward(QuestReward reward, Player player)
        {
            switch (reward.rewardType)
            {
                case QuestRewardType.Experience:
                    player.AddExperience(reward.value);
                    break;
                
                case QuestRewardType.AttributeBonus:
                    // 永久属性加成
                    switch (reward.attributeType)
                    {
                        case AttributeType.Physique:
                            player.basePhysique += reward.value;
                            break;
                        case AttributeType.Charm:
                            player.baseCharm += reward.value;
                            break;
                        case AttributeType.Intelligence:
                            player.baseIntelligence += reward.value;
                            break;
                        case AttributeType.Fortune:
                            player.baseFortune += reward.value;
                            break;
                        case AttributeType.Spirit:
                            player.baseSpirit += reward.value;
                            break;
                    }
                    break;
                
                case QuestRewardType.DrawCard:
                    for (int i = 0; i < reward.value; i++)
                    {
                        player.DrawCard();
                    }
                    break;
                
                case QuestRewardType.UnlockCard:
                    player.unlockedCards.Add(reward.value);
                    break;
                
                case QuestRewardType.UnlockRite:
                    player.unlockedRites.Add(reward.value);
                    break;
                
                case QuestRewardType.Healing:
                    player.Heal(reward.value);
                    break;
            }
        }
        
        /// <summary>
        /// 解锁后续任务
        /// </summary>
        private void UnlockFollowUpQuests(QuestData completedQuest)
        {
            foreach (var quest in allQuests)
            {
                if (quest.prerequisiteQuests.Contains(completedQuest.id) && !quest.isUnlocked)
                {
                    quest.isUnlocked = true;
                    availableQuests.Add(quest);
                    Debug.Log($"🔓 解锁任务: {quest.questName}");
                }
            }
        }
        
        /// <summary>
        /// 更新可用任务
        /// </summary>
        private void UpdateAvailableQuests()
        {
            availableQuests.Clear();
            
            foreach (var quest in allQuests)
            {
                if (quest.CanStart() && !activeQuests.Contains(quest) && !completedQuests.Contains(quest))
                {
                    availableQuests.Add(quest);
                }
            }
        }
        
        /// <summary>
        /// 放弃任务
        /// </summary>
        public bool AbandonQuest(int questId)
        {
            var quest = activeQuests.FirstOrDefault(q => q.id == questId);
            if (quest == null) return false;
            
            if (quest.isMainQuest)
            {
                Debug.LogWarning("无法放弃主线任务");
                return false;
            }
            
            activeQuests.Remove(quest);
            quest.Reset();
            
            if (quest.CanStart())
            {
                availableQuests.Add(quest);
            }
            
            GameEventSystem.Publish(new QuestEventArgs(questId, QuestEventType.Abandoned, quest.questName));
            
            Debug.Log($"❌ 放弃任务: {quest.questName}");
            return true;
        }
        
        /// <summary>
        /// 获取任务进度信息
        /// </summary>
        public string GetQuestProgressInfo(int questId)
        {
            var quest = activeQuests.FirstOrDefault(q => q.id == questId);
            if (quest == null) return "任务未找到";
            
            return quest.GetProgressInfo();
        }
        
        /// <summary>
        /// 处理卡牌事件
        /// </summary>
        private void OnCardEvent(CardEventArgs args)
        {
            UpdateQuestProgress();
        }
        
        /// <summary>
        /// 处理玩家事件
        /// </summary>
        private void OnPlayerEvent(PlayerEventArgs args)
        {
            UpdateQuestProgress();
        }
        
        /// <summary>
        /// 处理仪式事件
        /// </summary>
        private void OnRiteEvent(RiteEventArgs args)
        {
            UpdateQuestProgress();
        }
        
        private void OnDestroy()
        {
            GameEventSystem.Unsubscribe<CardEventArgs>(OnCardEvent);
            GameEventSystem.Unsubscribe<PlayerEventArgs>(OnPlayerEvent);
            GameEventSystem.Unsubscribe<RiteEventArgs>(OnRiteEvent);
        }
    }
}
