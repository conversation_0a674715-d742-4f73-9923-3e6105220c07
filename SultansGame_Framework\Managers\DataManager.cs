using System;
using System.IO;
using UnityEngine;
using SultansGameClone.Core;

namespace SultansGameClone.Managers
{
    /// <summary>
    /// 数据管理器 - 处理游戏数据的保存和加载
    /// </summary>
    public class DataManager : MonoBehaviour
    {
        [Header("保存设置")]
        public bool useEncryption = true;
        public string saveFileName = "sultans_game_save.dat";
        public string backupFileName = "sultans_game_backup.dat";
        
        [Header("自动备份")]
        public bool autoBackup = true;
        public int maxBackupFiles = 5;
        
        // 单例
        public static DataManager Instance { get; private set; }
        
        // 保存路径
        private string SavePath => Path.Combine(Application.persistentDataPath, saveFileName);
        private string BackupPath => Path.Combine(Application.persistentDataPath, backupFileName);
        
        // 事件
        public event Action OnSaveCompleted;
        public event Action OnLoadCompleted;
        public event Action<string> OnSaveError;
        public event Action<string> OnLoadError;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeDataManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 初始化数据管理器
        /// </summary>
        private void InitializeDataManager()
        {
            Debug.Log("💾 初始化数据管理器...");
            
            // 确保保存目录存在
            string saveDirectory = Path.GetDirectoryName(SavePath);
            if (!Directory.Exists(saveDirectory))
            {
                Directory.CreateDirectory(saveDirectory);
            }
            
            Debug.Log($"✅ 数据管理器初始化完成 - 保存路径: {SavePath}");
        }
        
        /// <summary>
        /// 保存玩家数据
        /// </summary>
        public bool SavePlayerData(Player player)
        {
            if (player == null)
            {
                Debug.LogError("无法保存空的玩家数据");
                OnSaveError?.Invoke("玩家数据为空");
                return false;
            }
            
            try
            {
                // 创建保存数据
                var saveData = new SaveData
                {
                    player = player,
                    saveTime = DateTime.Now,
                    gameVersion = Application.version,
                    playTime = Time.time
                };
                
                // 序列化数据
                string jsonData = JsonUtility.ToJson(saveData, true);
                
                // 加密数据（如果启用）
                if (useEncryption)
                {
                    jsonData = EncryptData(jsonData);
                }
                
                // 创建备份（如果启用）
                if (autoBackup && File.Exists(SavePath))
                {
                    CreateBackup();
                }
                
                // 写入文件
                File.WriteAllText(SavePath, jsonData);
                
                OnSaveCompleted?.Invoke();
                Debug.Log($"💾 玩家数据保存成功 - {player.playerName}");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"保存玩家数据失败: {e.Message}");
                OnSaveError?.Invoke(e.Message);
                return false;
            }
        }
        
        /// <summary>
        /// 加载玩家数据
        /// </summary>
        public Player LoadPlayerData()
        {
            if (!HasSaveData())
            {
                Debug.LogWarning("没有找到保存数据");
                OnLoadError?.Invoke("没有找到保存数据");
                return null;
            }
            
            try
            {
                // 读取文件
                string jsonData = File.ReadAllText(SavePath);
                
                // 解密数据（如果启用）
                if (useEncryption)
                {
                    jsonData = DecryptData(jsonData);
                }
                
                // 反序列化数据
                var saveData = JsonUtility.FromJson<SaveData>(jsonData);
                
                if (saveData?.player == null)
                {
                    Debug.LogError("保存数据损坏");
                    OnLoadError?.Invoke("保存数据损坏");
                    return null;
                }
                
                OnLoadCompleted?.Invoke();
                Debug.Log($"📂 玩家数据加载成功 - {saveData.player.playerName}");
                return saveData.player;
            }
            catch (Exception e)
            {
                Debug.LogError($"加载玩家数据失败: {e.Message}");
                OnLoadError?.Invoke(e.Message);
                
                // 尝试从备份恢复
                return LoadFromBackup();
            }
        }
        
        /// <summary>
        /// 检查是否有保存数据
        /// </summary>
        public bool HasSaveData()
        {
            return File.Exists(SavePath);
        }
        
        /// <summary>
        /// 删除保存数据
        /// </summary>
        public bool DeleteSaveData()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    File.Delete(SavePath);
                }
                
                if (File.Exists(BackupPath))
                {
                    File.Delete(BackupPath);
                }
                
                Debug.Log("🗑️ 保存数据已删除");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"删除保存数据失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 创建备份
        /// </summary>
        private void CreateBackup()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    File.Copy(SavePath, BackupPath, true);
                    Debug.Log("📋 创建数据备份");
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"创建备份失败: {e.Message}");
            }
        }
        
        /// <summary>
        /// 从备份恢复
        /// </summary>
        private Player LoadFromBackup()
        {
            if (!File.Exists(BackupPath))
            {
                Debug.LogError("备份文件不存在");
                return null;
            }
            
            try
            {
                string jsonData = File.ReadAllText(BackupPath);
                
                if (useEncryption)
                {
                    jsonData = DecryptData(jsonData);
                }
                
                var saveData = JsonUtility.FromJson<SaveData>(jsonData);
                
                if (saveData?.player != null)
                {
                    Debug.Log("📂 从备份恢复数据成功");
                    return saveData.player;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"从备份恢复失败: {e.Message}");
            }
            
            return null;
        }
        
        /// <summary>
        /// 加密数据
        /// </summary>
        private string EncryptData(string data)
        {
            // 简单的Base64编码（实际项目中应使用更强的加密）
            byte[] bytes = System.Text.Encoding.UTF8.GetBytes(data);
            return Convert.ToBase64String(bytes);
        }
        
        /// <summary>
        /// 解密数据
        /// </summary>
        private string DecryptData(string encryptedData)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(encryptedData);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                // 如果解密失败，可能是未加密的数据
                return encryptedData;
            }
        }
        
        /// <summary>
        /// 获取保存数据信息
        /// </summary>
        public SaveInfo GetSaveInfo()
        {
            if (!HasSaveData()) return null;
            
            try
            {
                var fileInfo = new FileInfo(SavePath);
                string jsonData = File.ReadAllText(SavePath);
                
                if (useEncryption)
                {
                    jsonData = DecryptData(jsonData);
                }
                
                var saveData = JsonUtility.FromJson<SaveData>(jsonData);
                
                return new SaveInfo
                {
                    playerName = saveData.player?.playerName ?? "未知",
                    playerLevel = saveData.player?.level ?? 0,
                    saveTime = saveData.saveTime,
                    playTime = saveData.playTime,
                    fileSize = fileInfo.Length,
                    gameVersion = saveData.gameVersion
                };
            }
            catch (Exception e)
            {
                Debug.LogError($"获取保存信息失败: {e.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 导出保存数据
        /// </summary>
        public bool ExportSaveData(string exportPath)
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    File.Copy(SavePath, exportPath, true);
                    Debug.Log($"📤 保存数据导出到: {exportPath}");
                    return true;
                }
                return false;
            }
            catch (Exception e)
            {
                Debug.LogError($"导出保存数据失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 导入保存数据
        /// </summary>
        public bool ImportSaveData(string importPath)
        {
            try
            {
                if (File.Exists(importPath))
                {
                    // 创建当前数据的备份
                    if (HasSaveData())
                    {
                        CreateBackup();
                    }
                    
                    File.Copy(importPath, SavePath, true);
                    Debug.Log($"📥 保存数据导入自: {importPath}");
                    return true;
                }
                return false;
            }
            catch (Exception e)
            {
                Debug.LogError($"导入保存数据失败: {e.Message}");
                return false;
            }
        }
    }
    
    /// <summary>
    /// 保存数据结构
    /// </summary>
    [Serializable]
    public class SaveData
    {
        public Player player;
        public DateTime saveTime;
        public string gameVersion;
        public float playTime;
    }
    
    /// <summary>
    /// 保存信息
    /// </summary>
    [Serializable]
    public class SaveInfo
    {
        public string playerName;
        public int playerLevel;
        public DateTime saveTime;
        public float playTime;
        public long fileSize;
        public string gameVersion;
        
        public string GetFormattedPlayTime()
        {
            var timeSpan = TimeSpan.FromSeconds(playTime);
            return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
        }
        
        public string GetFormattedFileSize()
        {
            if (fileSize < 1024)
                return $"{fileSize} B";
            else if (fileSize < 1024 * 1024)
                return $"{fileSize / 1024:F1} KB";
            else
                return $"{fileSize / (1024 * 1024):F1} MB";
        }
    }
}
