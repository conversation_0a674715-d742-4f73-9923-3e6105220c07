using System;
using System.Collections.Generic;
using UnityEngine;

namespace SultansGameClone
{
    /// <summary>
    /// 游戏配置 - 存储游戏的各种设置和参数
    /// </summary>
    [CreateAssetMenu(fileName = "GameConfig", menuName = "Sultan's Game/Game Config")]
    public class GameConfig : ScriptableObject
    {
        [Header("游戏基础设置")]
        public string gameVersion = "1.0.0";
        public string gameName = "Sultan's Game Clone";
        public bool debugMode = false;
        
        [Header("玩家设置")]
        public int maxPlayerLevel = 100;
        public int baseExperiencePerLevel = 100;
        public float experienceMultiplier = 1.2f;
        public int startingHealth = 100;
        public int maxHealth = 200;
        
        [Header("卡牌设置")]
        public int maxHandSize = 10;
        public int maxTableSize = 5;
        public int initialDeckSize = 20;
        public int maxCardLevel = 10;
        public int cardExperiencePerLevel = 50;
        
        [Header("属性设置")]
        public int baseAttributeValue = 10;
        public int maxAttributeValue = 100;
        public int attributeBonusPerLevel = 2;
        
        [Header("仪式设置")]
        public int maxActiveRites = 3;
        public float riteCompletionBonus = 1.5f;
        public int riteExperienceReward = 100;
        
        [Header("任务设置")]
        public int maxActiveQuests = 5;
        public int dailyQuestCount = 3;
        public int weeklyQuestCount = 1;
        public float questExperienceMultiplier = 1.0f;
        
        [Header("经济设置")]
        public int startingCurrency = 100;
        public int maxCurrency = 999999;
        public float inflationRate = 1.05f;
        
        [Header("难度设置")]
        public DifficultySettings easyDifficulty;
        public DifficultySettings normalDifficulty;
        public DifficultySettings hardDifficulty;
        public DifficultySettings expertDifficulty;
        
        [Header("UI设置")]
        public float animationSpeed = 1.0f;
        public bool enableParticleEffects = true;
        public bool enableScreenShake = true;
        public float uiFadeTime = 0.3f;
        
        [Header("音频设置")]
        public float defaultMasterVolume = 1.0f;
        public float defaultMusicVolume = 0.7f;
        public float defaultSFXVolume = 0.8f;
        public float defaultVoiceVolume = 1.0f;
        
        [Header("保存设置")]
        public bool autoSave = true;
        public float autoSaveInterval = 300f; // 5分钟
        public bool enableCloudSave = false;
        public int maxSaveSlots = 3;
        
        [Header("性能设置")]
        public int targetFrameRate = 60;
        public bool enableVSync = true;
        public QualityLevel defaultQuality = QualityLevel.High;
        
        [Header("本地化设置")]
        public SystemLanguage defaultLanguage = SystemLanguage.Chinese;
        public List<SystemLanguage> supportedLanguages = new List<SystemLanguage>
        {
            SystemLanguage.Chinese,
            SystemLanguage.English,
            SystemLanguage.Japanese
        };
        
        [Header("调试设置")]
        public bool showDebugInfo = false;
        public bool enableCheats = false;
        public bool skipTutorial = false;
        public bool unlockAllCards = false;
        
        /// <summary>
        /// 获取指定等级所需的经验值
        /// </summary>
        public int GetRequiredExperience(int level)
        {
            return Mathf.RoundToInt(baseExperiencePerLevel * Mathf.Pow(experienceMultiplier, level - 1));
        }
        
        /// <summary>
        /// 获取卡牌升级所需经验
        /// </summary>
        public int GetCardUpgradeExperience(int level)
        {
            return cardExperiencePerLevel * level;
        }
        
        /// <summary>
        /// 获取难度设置
        /// </summary>
        public DifficultySettings GetDifficultySettings(Core.GameDifficulty difficulty)
        {
            return difficulty switch
            {
                Core.GameDifficulty.Easy => easyDifficulty,
                Core.GameDifficulty.Normal => normalDifficulty,
                Core.GameDifficulty.Hard => hardDifficulty,
                Core.GameDifficulty.Expert => expertDifficulty,
                _ => normalDifficulty
            };
        }
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        public bool ValidateConfig()
        {
            bool isValid = true;
            
            if (maxPlayerLevel <= 0)
            {
                Debug.LogError("最大玩家等级必须大于0");
                isValid = false;
            }
            
            if (baseExperiencePerLevel <= 0)
            {
                Debug.LogError("基础经验值必须大于0");
                isValid = false;
            }
            
            if (maxHandSize <= 0)
            {
                Debug.LogError("最大手牌数必须大于0");
                isValid = false;
            }
            
            if (maxTableSize <= 0)
            {
                Debug.LogError("最大桌面卡牌数必须大于0");
                isValid = false;
            }
            
            if (autoSaveInterval <= 0)
            {
                Debug.LogError("自动保存间隔必须大于0");
                isValid = false;
            }
            
            return isValid;
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            gameVersion = "1.0.0";
            gameName = "Sultan's Game Clone";
            debugMode = false;
            
            maxPlayerLevel = 100;
            baseExperiencePerLevel = 100;
            experienceMultiplier = 1.2f;
            startingHealth = 100;
            maxHealth = 200;
            
            maxHandSize = 10;
            maxTableSize = 5;
            initialDeckSize = 20;
            maxCardLevel = 10;
            cardExperiencePerLevel = 50;
            
            baseAttributeValue = 10;
            maxAttributeValue = 100;
            attributeBonusPerLevel = 2;
            
            maxActiveRites = 3;
            riteCompletionBonus = 1.5f;
            riteExperienceReward = 100;
            
            maxActiveQuests = 5;
            dailyQuestCount = 3;
            weeklyQuestCount = 1;
            questExperienceMultiplier = 1.0f;
            
            startingCurrency = 100;
            maxCurrency = 999999;
            inflationRate = 1.05f;
            
            animationSpeed = 1.0f;
            enableParticleEffects = true;
            enableScreenShake = true;
            uiFadeTime = 0.3f;
            
            defaultMasterVolume = 1.0f;
            defaultMusicVolume = 0.7f;
            defaultSFXVolume = 0.8f;
            defaultVoiceVolume = 1.0f;
            
            autoSave = true;
            autoSaveInterval = 300f;
            enableCloudSave = false;
            maxSaveSlots = 3;
            
            targetFrameRate = 60;
            enableVSync = true;
            defaultQuality = QualityLevel.High;
            
            defaultLanguage = SystemLanguage.Chinese;
            
            showDebugInfo = false;
            enableCheats = false;
            skipTutorial = false;
            unlockAllCards = false;
        }
    }
    
    /// <summary>
    /// 难度设置
    /// </summary>
    [Serializable]
    public class DifficultySettings
    {
        [Header("经验设置")]
        public float experienceMultiplier = 1.0f;
        public float questExperienceMultiplier = 1.0f;
        public float riteExperienceMultiplier = 1.0f;
        
        [Header("属性设置")]
        public float attributeGrowthRate = 1.0f;
        public int startingAttributeBonus = 0;
        
        [Header("卡牌设置")]
        public float cardDropRateMultiplier = 1.0f;
        public float rareCardChance = 0.1f;
        public int startingCardBonus = 0;
        
        [Header("生命值设置")]
        public float healthMultiplier = 1.0f;
        public float healingEffectiveness = 1.0f;
        
        [Header("任务设置")]
        public float questDifficultyMultiplier = 1.0f;
        public int additionalQuestObjectives = 0;
        
        [Header("仪式设置")]
        public float riteRequirementMultiplier = 1.0f;
        public float riteRewardMultiplier = 1.0f;
        
        [Header("其他设置")]
        public bool enableHints = true;
        public bool showTutorial = true;
        public float timeMultiplier = 1.0f;
    }
    
    /// <summary>
    /// 质量等级
    /// </summary>
    public enum QualityLevel
    {
        Low,        // 低
        Medium,     // 中
        High,       // 高
        Ultra       // 超高
    }
}
